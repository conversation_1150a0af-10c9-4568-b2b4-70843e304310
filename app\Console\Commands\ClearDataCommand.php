<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Estimate;
use App\Models\EstimateItem;
use App\Models\MetalRate;
use App\Models\Repair;
use App\Models\OldGoldPurchase;
use App\Models\SavingScheme;
use App\Models\SchemePayment;

class ClearDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:clear {--confirm : Confirm the data clearing operation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all data except users and roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!$this->option('confirm')) {
            $this->error('This command will delete all data except users and roles!');
            $this->info('Use --confirm flag to proceed: php artisan data:clear --confirm');
            return 1;
        }

        $this->info('Clearing all data except users and roles...');

        // Clear data in proper order (respecting foreign key constraints)
        $this->clearTable(SchemePayment::class, 'scheme_payments');
        $this->clearTable(SavingScheme::class, 'saving_schemes');
        $this->clearTable(SaleItem::class, 'sale_items');
        $this->clearTable(Sale::class, 'sales');
        $this->clearTable(EstimateItem::class, 'estimate_items');
        $this->clearTable(Estimate::class, 'estimates');
        $this->clearTable(Repair::class, 'repairs');
        $this->clearTable(OldGoldPurchase::class, 'old_gold_purchases');
        $this->clearTable(Product::class, 'products');
        $this->clearTable(Customer::class, 'customers');
        $this->clearTable(MetalRate::class, 'metal_rates');

        $this->info('All data cleared successfully!');
        return 0;
    }

    private function clearTable($model, $tableName)
    {
        $count = $model::count();
        if ($count > 0) {
            $model::truncate();
            $this->line("Cleared {$count} records from {$tableName}");
        } else {
            $this->line("No records to clear from {$tableName}");
        }
    }
}
