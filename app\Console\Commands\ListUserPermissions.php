<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class ListUserPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:permissions {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all permissions for a user by email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        $user = User::where('email', $email)->first();
        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }

        $this->info("User: {$user->name} ({$user->email})");
        $this->info("Roles: " . $user->roles->pluck('name')->implode(', '));

        $permissions = $user->getAllPermissions();
        if ($permissions->count() > 0) {
            $this->info("Permissions:");
            foreach ($permissions as $permission) {
                $this->line("  - {$permission->name}");
            }
        } else {
            $this->warn("No permissions found for this user.");
        }

        // Check specific metal rate permissions
        $metalRatePermissions = [
            'view_metal_rates',
            'create_metal_rate',
            'edit_metal_rate',
            'delete_metal_rate',
            'toggle_metal_rate_status',
            'bulk_manage_metal_rates'
        ];

        $this->info("\nMetal Rate Permissions Check:");
        foreach ($metalRatePermissions as $permission) {
            $hasPermission = $user->can($permission) ? '✅' : '❌';
            $this->line("  {$hasPermission} {$permission}");
        }

        return 0;
    }
}
