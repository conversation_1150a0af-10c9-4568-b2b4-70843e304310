<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Models\Customer;
use App\Models\MetalRate;
use App\Models\User;

class ShowSampleDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:show';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show sample data summary';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== SAMPLE DATA SUMMARY ===');
        $this->newLine();

        // Products
        $this->info('📦 PRODUCTS (' . Product::count() . ' records):');
        $products = Product::select('name', 'huid_number', 'metal_type', 'purity', 'status')->get();
        foreach ($products as $product) {
            $this->line("  • {$product->name} | HUID: {$product->huid_number} | {$product->metal_type} {$product->purity} | Status: {$product->status}");
        }
        $this->newLine();

        // Customers
        $this->info('👥 CUSTOMERS (' . Customer::count() . ' records):');
        $customers = Customer::select('name', 'mobile', 'city', 'state')->get();
        foreach ($customers as $customer) {
            $this->line("  • {$customer->name} | {$customer->mobile} | {$customer->city}, {$customer->state}");
        }
        $this->newLine();

        // Metal Rates
        $this->info('💰 METAL RATES (' . MetalRate::count() . ' records):');
        $rates = MetalRate::select('metal_type', 'purity', 'rate_per_gram')->get();
        foreach ($rates as $rate) {
            $this->line("  • {$rate->metal_type} {$rate->purity}: ₹{$rate->rate_per_gram}/gram");
        }
        $this->newLine();

        // Users
        $this->info('👤 USERS (' . User::count() . ' records):');
        $users = User::with('roles')->get();
        foreach ($users as $user) {
            $roles = $user->roles->pluck('name')->join(', ');
            $this->line("  • {$user->name} ({$user->email}) | Roles: {$roles}");
        }
        $this->newLine();

        $this->info('✅ Sample data is ready for testing!');
        $this->info('🔗 Login credentials: <EMAIL> / password123');
    }
}
