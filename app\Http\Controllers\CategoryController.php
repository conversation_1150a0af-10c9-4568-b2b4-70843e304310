<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use Illuminate\Support\Facades\Storage;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:view_categories')->only(['index', 'show']);
        $this->middleware('permission:create_category')->only(['create', 'store']);
        $this->middleware('permission:edit_category')->only(['edit', 'update']);
        $this->middleware('permission:delete_category')->only(['destroy']);
        $this->middleware('permission:toggle_category_status')->only(['toggleActive']);
        $this->middleware('permission:bulk_manage_categories')->only(['bulkAction']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Category::with(['parent', 'children']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by type (parent/child)
        if ($request->filled('type')) {
            if ($request->type === 'parent') {
                $query->whereNull('parent_id');
            } elseif ($request->type === 'child') {
                $query->whereNotNull('parent_id');
            }
        }

        $categories = $query->ordered()->paginate(15);

        // Get parent categories for filter dropdown
        $parentCategories = Category::parents()->active()->ordered()->get();

        return view('categories.index', compact('categories', 'parentCategories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $parentCategories = Category::parents()->active()->ordered()->get();
        return view('categories.create', compact('parentCategories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name',
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image_path'] = $request->file('image')
                ->store('categories', 'public');
        }

        $validated['is_active'] = $request->has('is_active');

        Category::create($validated);

        return redirect()->route('categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Category $category)
    {
        $category->load(['parent', 'children', 'products']);

        // Get products count for this category and its children
        $productsCount = $category->products()->count();
        if ($category->hasChildren()) {
            $childrenIds = $category->children->pluck('id');
            $productsCount += \App\Models\Product::whereIn('category_id', $childrenIds)->count();
        }

        return view('categories.show', compact('category', 'productsCount'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Category $category)
    {
        $parentCategories = Category::parents()
            ->active()
            ->where('id', '!=', $category->id)
            ->ordered()
            ->get();

        return view('categories.edit', compact('category', 'parentCategories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:categories,name,' . $category->id,
            'description' => 'nullable|string|max:1000',
            'parent_id' => 'nullable|exists:categories,id',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        // Prevent setting self as parent
        if ($validated['parent_id'] == $category->id) {
            return redirect()->back()
                ->withErrors(['parent_id' => 'Category cannot be its own parent.'])
                ->withInput();
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($category->image_path) {
                Storage::disk('public')->delete($category->image_path);
            }
            $validated['image_path'] = $request->file('image')
                ->store('categories', 'public');
        }

        $validated['is_active'] = $request->has('is_active');

        $category->update($validated);

        return redirect()->route('categories.index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Category $category)
    {
        // Check if category has products
        if ($category->products()->count() > 0) {
            return redirect()->route('categories.index')
                ->with('error', 'Cannot delete category with existing products.');
        }

        // Check if category has children
        if ($category->hasChildren()) {
            return redirect()->route('categories.index')
                ->with('error', 'Cannot delete category with subcategories. Delete subcategories first.');
        }

        // Delete image if exists
        if ($category->image_path) {
            Storage::disk('public')->delete($category->image_path);
        }

        $category->delete();

        return redirect()->route('categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Toggle category active status
     */
    public function toggleActive(Category $category)
    {
        $category->update(['is_active' => !$category->is_active]);

        $status = $category->is_active ? 'activated' : 'deactivated';
        return redirect()->route('categories.index')
            ->with('success', "Category {$status} successfully.");
    }

    /**
     * Bulk actions for categories
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'categories' => 'required|array|min:1',
            'categories.*' => 'exists:categories,id',
        ]);

        $categories = Category::whereIn('id', $request->categories);
        $count = $categories->count();

        switch ($request->action) {
            case 'activate':
                $categories->update(['is_active' => true]);
                break;
            case 'deactivate':
                $categories->update(['is_active' => false]);
                break;
            case 'delete':
                // Check for products or children before deleting
                $categoriesWithProducts = Category::whereIn('id', $request->categories)
                    ->whereHas('products')
                    ->count();

                $categoriesWithChildren = Category::whereIn('id', $request->categories)
                    ->whereHas('children')
                    ->count();

                if ($categoriesWithProducts > 0 || $categoriesWithChildren > 0) {
                    return redirect()->route('categories.index')
                        ->with('error', 'Cannot delete categories with products or subcategories.');
                }

                // Delete images
                $categoriesToDelete = Category::whereIn('id', $request->categories)->get();
                foreach ($categoriesToDelete as $category) {
                    if ($category->image_path) {
                        Storage::disk('public')->delete($category->image_path);
                    }
                }

                $categories->delete();
                break;
        }

        $actionText = [
            'activate' => 'activated',
            'deactivate' => 'deactivated',
            'delete' => 'deleted',
        ];

        return redirect()->route('categories.index')
            ->with('success', "{$count} category(ies) {$actionText[$request->action]} successfully.");
    }
}
