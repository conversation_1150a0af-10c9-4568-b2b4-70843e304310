<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Sale;
use App\Models\Estimate;
use App\Models\Repair;
use App\Models\MetalRate;
use App\Models\SavingScheme;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Get dashboard statistics
        $stats = [
            'total_customers' => Customer::count(),
            'total_products' => Product::where('status', 'in_stock')->count(),
            'today_sales' => Sale::whereDate('sale_date', today())->sum('total_amount'),
            'pending_estimates' => Estimate::where('status', 'pending')->count(),
            'pending_repairs' => Repair::where('status', '!=', 'delivered')->count(),
            // Saving scheme statistics
            'active_schemes' => SavingScheme::where('status', 'active')->count(),
            'matured_schemes' => SavingScheme::where('status', 'matured')->count(),
            'overdue_schemes' => SavingScheme::where('status', 'overdue')->count(),
            'total_collections' => SavingScheme::sum('total_paid'),
            'pending_collections' => SavingScheme::where('status', 'active')->get()->sum(function($scheme) {
                return $scheme->target_amount - $scheme->total_paid;
            }),
            // Additional statistics
            'total_sales_mtd' => Sale::whereMonth('sale_date', now()->month)
                ->whereYear('sale_date', now()->year)
                ->sum('total_amount'),
            'total_sales_ytd' => Sale::whereYear('sale_date', now()->year)
                ->sum('total_amount'),
        ];

        // Get recent sales
        $recent_sales = Sale::with('customer')
            ->latest()
            ->take(5)
            ->get();

        // Get current metal rates (simplified to avoid collection issues)
        $metal_rates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->limit(5)
            ->get();
            
        // Get monthly sales data for chart
        $monthly_sales = Sale::select(
                DB::raw("strftime('%m', sale_date) as month"), 
                DB::raw("strftime('%Y', sale_date) as year"),
                DB::raw('SUM(total_amount) as total')
            )
            ->whereRaw("strftime('%Y', sale_date) = ?", [Carbon::now()->year])
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function ($item) {
                $monthName = Carbon::createFromDate($item->year, $item->month, 1)->format('M');
                return [
                    'month' => $monthName,
                    'total' => $item->total
                ];
            });

        // Fill in missing months with zero values
        $allMonths = [];
        for ($i = 1; $i <= 12; $i++) {
            $monthName = Carbon::createFromDate(null, $i, 1)->format('M');
            $allMonths[$monthName] = 0;
        }
        
        foreach ($monthly_sales as $item) {
            $allMonths[$item['month']] = $item['total'];
        }
        
        $monthly_sales = collect(array_map(function($month, $total) {
            return ['month' => $month, 'total' => $total];
        }, array_keys($allMonths), array_values($allMonths)));

        return view('dashboard', compact('stats', 'recent_sales', 'metal_rates', 'monthly_sales'));
    }
}
