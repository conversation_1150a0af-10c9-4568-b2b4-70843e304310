<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\MetalRate;
use Illuminate\Support\Facades\Storage;
use Milon\Barcode\DNS1D;
use App\Services\HuidValidationService;
use App\Services\TagNumberService;
use App\Rules\ValidHuid;
use App\Rules\HuidRequired;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:view_inventory')->only(['index', 'show']);
        $this->middleware('permission:create_product')->only(['create', 'store']);
        $this->middleware('permission:edit_product')->only(['edit', 'update']);
        $this->middleware('permission:delete_product')->only(['destroy']);
        $this->middleware('permission:manage_barcode')->only(['generateBarcode']);
        $this->middleware('permission:bulk_manage_products')->only(['bulkAction']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('barcode', 'like', "%{$search}%")
                  ->orWhere('huid_number', 'like', "%{$search}%")
                  ->orWhere('tag_number', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Category filter
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Metal type filter
        if ($request->filled('metal_type')) {
            $query->where('metal_type', $request->metal_type);
        }

        // HUID filter
        if ($request->filled('huid_filter')) {
            switch ($request->huid_filter) {
                case 'with_huid':
                    $query->whereNotNull('huid_number');
                    break;
                case 'without_huid':
                    $query->whereNull('huid_number');
                    break;
                case 'huid_required':
                    $query->where('huid_required', true)->whereNull('huid_number');
                    break;
            }
        }

        $products = $query->latest()->paginate(15);

        // Get filter options
        $categories = Category::active()->ordered()->get();
        $metalTypes = Product::distinct()->pluck('metal_type')->filter();
        $statuses = ['in_stock' => 'In Stock', 'sold' => 'Sold', 'reserved' => 'Reserved', 'repair' => 'Under Repair'];

        // Get inventory alerts
        $lowStockCount = Product::where('quantity', '<=', 5)->where('status', 'in_stock')->count();
        $huidRequiredCount = Product::where('huid_required', true)->whereNull('huid_number')->count();

        return view('products.index', compact('products', 'categories', 'metalTypes', 'statuses', 'lowStockCount', 'huidRequiredCount'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get current metal rates for pricing calculation
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        // Get active categories for dropdown
        $categories = Category::active()->ordered()->get();

        return view('products.create', compact('metalRates', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // First validate basic fields
        $basicValidation = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'category' => 'nullable|string|max:255',
            'metal_type' => 'required|string|max:255',
            'purity' => 'required|string|max:255',
            'gross_weight' => 'required|numeric|min:0',
            'net_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'making_charges' => 'required|numeric|min:0',
            'stone_charges' => 'nullable|numeric|min:0',
            'hsn_code' => 'nullable|string|max:10',
            'tag_number' => 'nullable|string|max:20|unique:products,tag_number',
            'barcode' => 'nullable|string|max:255|unique:products,barcode',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:1',
            'size' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'has_stones' => 'boolean',
            'stone_details' => 'nullable|array',
        ]);

        // HUID validation with context
        $huidValidation = $request->validate([
            'huid_number' => [
                'nullable',
                'string',
                'max:6',
                'unique:products,huid_number',
                new ValidHuid($basicValidation['metal_type'], $basicValidation['net_weight'])
            ],
        ]);

        $validated = array_merge($basicValidation, $huidValidation);

        // Set category name from selected category for backward compatibility
        if ($validated['category_id']) {
            $category = Category::find($validated['category_id']);
            $validated['category'] = $category ? $category->name : '';
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image_path'] = $request->file('image')
                ->store('product-images', 'public');
        }

        // Set default values
        $validated['stone_weight'] = $validated['stone_weight'] ?? 0;
        $validated['stone_charges'] = $validated['stone_charges'] ?? 0;
        $validated['wastage_percentage'] = $validated['wastage_percentage'] ?? 0;
        $validated['hsn_code'] = $validated['hsn_code'] ?? '71131900';
        $validated['has_stones'] = $request->has('has_stones');

        // Set HUID compliance fields
        $validated['huid_required'] = HuidValidationService::isHuidRequired(
            $validated['metal_type'],
            $validated['net_weight']
        );

        // Store HUID if provided (no AHC code extraction needed for 6-character format)
        if (!empty($validated['huid_number'])) {
            $huidValidation = HuidValidationService::validateHuidFormat($validated['huid_number']);
            if ($huidValidation['valid']) {
                // For 6-character HUID, we store the full HUID as is
                $validated['huid_number'] = $huidValidation['huid'];
            }
        }

        $product = Product::create($validated);

        return redirect()->route('products.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        return view('products.show', compact('product'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        // Get current metal rates for pricing calculation
        $metalRates = MetalRate::where('is_active', true)
            ->where('effective_date', '<=', today())
            ->orderBy('effective_date', 'desc')
            ->get()
            ->groupBy(['metal_type', 'purity'])
            ->map(function ($group) {
                return $group->first();
            });

        // Get active categories for dropdown
        $categories = Category::active()->ordered()->get();

        return view('products.edit', compact('product', 'metalRates', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        // First validate basic fields
        $basicValidation = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'category' => 'nullable|string|max:255',
            'metal_type' => 'required|string|max:255',
            'purity' => 'required|string|max:255',
            'gross_weight' => 'required|numeric|min:0',
            'net_weight' => 'required|numeric|min:0',
            'stone_weight' => 'nullable|numeric|min:0',
            'wastage_percentage' => 'nullable|numeric|min:0|max:100',
            'making_charges' => 'required|numeric|min:0',
            'stone_charges' => 'nullable|numeric|min:0',
            'hsn_code' => 'nullable|string|max:10',
            'tag_number' => 'nullable|string|max:20|unique:products,tag_number,' . $product->id,
            'barcode' => 'nullable|string|max:255|unique:products,barcode,' . $product->id,
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'quantity' => 'required|integer|min:0',
            'size' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|file|mimes:jpg,jpeg,png|max:2048',
            'has_stones' => 'boolean',
            'stone_details' => 'nullable|array',
            'status' => 'required|in:in_stock,sold,reserved,repair',
        ]);

        // HUID validation with context
        $huidValidation = $request->validate([
            'huid_number' => [
                'nullable',
                'string',
                'max:6',
                'unique:products,huid_number,' . $product->id,
                new ValidHuid($basicValidation['metal_type'], $basicValidation['net_weight'])
            ],
        ]);

        $validated = array_merge($basicValidation, $huidValidation);

        // Set category name from selected category for backward compatibility
        if ($validated['category_id']) {
            $category = Category::find($validated['category_id']);
            $validated['category'] = $category ? $category->name : '';
        }

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($product->image_path) {
                Storage::disk('public')->delete($product->image_path);
            }

            $validated['image_path'] = $request->file('image')
                ->store('product-images', 'public');
        }

        // Set default values
        $validated['stone_weight'] = $validated['stone_weight'] ?? 0;
        $validated['stone_charges'] = $validated['stone_charges'] ?? 0;
        $validated['wastage_percentage'] = $validated['wastage_percentage'] ?? 0;
        $validated['hsn_code'] = $validated['hsn_code'] ?? '71131900';
        $validated['has_stones'] = $request->has('has_stones');

        // Set HUID compliance fields
        $validated['huid_required'] = HuidValidationService::isHuidRequired(
            $validated['metal_type'],
            $validated['net_weight']
        );

        // Store HUID if provided (no AHC code extraction needed for 6-character format)
        if (!empty($validated['huid_number'])) {
            $huidValidation = HuidValidationService::validateHuidFormat($validated['huid_number']);
            if ($huidValidation['valid']) {
                // For 6-character HUID, we store the full HUID as is
                $validated['huid_number'] = $huidValidation['huid'];
            }
        }

        $product->update($validated);

        return redirect()->route('products.index')
            ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        // Check if product has any related sales
        if ($product->saleItems()->count() > 0) {
            return redirect()->route('products.index')
                ->with('error', 'Cannot delete product with existing sales.');
        }

        // Delete product image if exists
        if ($product->image_path) {
            Storage::disk('public')->delete($product->image_path);
        }

        $product->delete();

        return redirect()->route('products.index')
            ->with('success', 'Product deleted successfully.');
    }

    /**
     * Generate and display barcode for a product
     */
    public function barcode(Product $product)
    {
        $barcode = new DNS1D();
        $barcodeImage = $barcode->getBarcodePNG($product->barcode, 'C128', 3, 33, array(1, 1, 1), true);

        return view('products.barcode', compact('product', 'barcodeImage'));
    }

    /**
     * Handle bulk actions on products
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,status_change',
            'product_ids' => 'required|array|min:1',
            'product_ids.*' => 'exists:products,id',
            'status' => 'required_if:action,status_change|in:in_stock,sold,reserved,repair'
        ]);

        $productIds = $request->product_ids;
        $action = $request->action;

        try {
            switch ($action) {
                case 'delete':
                    $count = Product::whereIn('id', $productIds)->count();
                    Product::whereIn('id', $productIds)->delete();
                    return redirect()->route('products.index')
                        ->with('success', "Successfully deleted {$count} product(s).");

                case 'status_change':
                    $status = $request->status;
                    $count = Product::whereIn('id', $productIds)
                        ->update(['status' => $status]);
                    $statusLabel = ucfirst(str_replace('_', ' ', $status));
                    return redirect()->route('products.index')
                        ->with('success', "Successfully updated {$count} product(s) to {$statusLabel}.");

                default:
                    return redirect()->route('products.index')
                        ->with('error', 'Invalid action selected.');
            }
        } catch (\Exception $e) {
            return redirect()->route('products.index')
                ->with('error', 'An error occurred while processing the bulk action: ' . $e->getMessage());
        }
    }

    /**
     * Toggle product status between in_stock and sold
     */
    public function toggleStatus(Product $product)
    {
        $newStatus = $product->status === 'in_stock' ? 'sold' : 'in_stock';
        $product->update(['status' => $newStatus]);

        $statusLabel = ucfirst(str_replace('_', ' ', $newStatus));
        return redirect()->back()
            ->with('success', "Product status updated to {$statusLabel}.");
    }

    /**
     * Adjust product quantity
     */
    public function adjustQuantity(Request $request, Product $product)
    {
        $request->validate([
            'adjustment_type' => 'required|in:increase,decrease,set',
            'quantity' => 'required|integer|min:0',
            'reason' => 'nullable|string|max:255'
        ]);

        $oldQuantity = $product->quantity;

        switch ($request->adjustment_type) {
            case 'increase':
                $newQuantity = $oldQuantity + $request->quantity;
                break;
            case 'decrease':
                $newQuantity = max(0, $oldQuantity - $request->quantity);
                break;
            case 'set':
                $newQuantity = $request->quantity;
                break;
        }

        $product->update(['quantity' => $newQuantity]);

        // Log the adjustment (you could create an InventoryAdjustment model for this)
        // For now, we'll just return success message

        return redirect()->back()
            ->with('success', "Quantity adjusted from {$oldQuantity} to {$newQuantity}.");
    }

    /**
     * Generate inventory report
     */
    public function inventoryReport(Request $request)
    {
        $query = Product::query();

        // Apply filters if provided
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->filled('metal_type')) {
            $query->where('metal_type', $request->metal_type);
        }

        $products = $query->get();

        // Calculate summary statistics
        $totalProducts = $products->count();
        $totalValue = $products->sum('selling_price');
        $inStockCount = $products->where('status', 'in_stock')->count();
        $soldCount = $products->where('status', 'sold')->count();
        $reservedCount = $products->where('status', 'reserved')->count();
        $repairCount = $products->where('status', 'repair')->count();
        $lowStockCount = $products->where('quantity', '<=', 5)->where('status', 'in_stock')->count();
        $huidRequiredCount = $products->where('huid_required', true)->whereNull('huid_number')->count();

        // Group by category
        $categoryStats = $products->groupBy('category')->map(function ($categoryProducts) {
            return [
                'count' => $categoryProducts->count(),
                'value' => $categoryProducts->sum('selling_price'),
                'in_stock' => $categoryProducts->where('status', 'in_stock')->count(),
            ];
        });

        // Group by metal type
        $metalStats = $products->groupBy('metal_type')->map(function ($metalProducts) {
            return [
                'count' => $metalProducts->count(),
                'value' => $metalProducts->sum('selling_price'),
                'weight' => $metalProducts->sum('net_weight'),
            ];
        });

        $categories = Category::active()->ordered()->get();
        $metalTypes = Product::distinct()->pluck('metal_type')->filter();
        $statuses = ['in_stock' => 'In Stock', 'sold' => 'Sold', 'reserved' => 'Reserved', 'repair' => 'Under Repair'];

        return view('products.inventory-report', compact(
            'products', 'totalProducts', 'totalValue', 'inStockCount', 'soldCount',
            'reservedCount', 'repairCount', 'lowStockCount', 'huidRequiredCount',
            'categoryStats', 'metalStats', 'categories', 'metalTypes', 'statuses'
        ));
    }
}
