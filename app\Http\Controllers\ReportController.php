<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;
use App\Models\Customer;
use App\Models\Product;
use App\Models\Repair;
use App\Models\SavingScheme;
use App\Models\OldGoldPurchase;
use App\Models\Estimate;
use App\Models\MetalRate;
use App\Models\SchemePayment;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Temporarily disable permission check for testing
        // $this->middleware('permission:view_reports');
    }

    /**
     * Reports dashboard
     */
    public function index()
    {
        // Get quick stats for the dashboard
        $stats = [
            'total_sales' => Sale::sum('total_amount'),
            'total_customers' => Customer::count(),
            'total_products' => Product::count(),
            'active_schemes' => SavingScheme::active()->count(),
            'pending_repairs' => Repair::whereNotIn('status', ['delivered'])->count(),
            'monthly_sales' => Sale::whereMonth('sale_date', now()->month)->sum('total_amount'),
            'monthly_customers' => Customer::whereMonth('created_at', now()->month)->count(),
            'overdue_repairs' => Repair::overdue()->count(),
        ];

        return view('reports.index', compact('stats'));
    }

    /**
     * Sales reports
     */
    public function salesReport(Request $request)
    {
        $fromDate = $request->get('from_date', now()->startOfMonth()->format('Y-m-d'));
        $toDate = $request->get('to_date', now()->format('Y-m-d'));
        $groupBy = $request->get('group_by', 'day');

        $query = Sale::whereBetween('sale_date', [$fromDate, $toDate]);

        // Sales summary
        $salesSummary = [
            'total_sales' => $query->sum('total_amount'),
            'total_orders' => $query->count(),
            'average_order_value' => $query->avg('total_amount'),
            'total_tax' => $query->sum('total_tax'),
            'total_discount' => $query->sum('discount_amount'),
        ];

        // Sales by period
        $salesByPeriod = $this->getSalesByPeriod($fromDate, $toDate, $groupBy);

        // Top customers
        $topCustomers = Sale::select('customer_id', DB::raw('SUM(total_amount) as total_spent'), DB::raw('COUNT(*) as order_count'))
            ->with('customer')
            ->whereBetween('sale_date', [$fromDate, $toDate])
            ->groupBy('customer_id')
            ->orderBy('total_spent', 'desc')
            ->limit(10)
            ->get();

        // Payment method breakdown
        $paymentMethodsQuery = Sale::whereBetween('sale_date', [$fromDate, $toDate])
            ->selectRaw('
                COALESCE(SUM(cash_payment), 0) as cash,
                COALESCE(SUM(card_payment), 0) as card,
                COALESCE(SUM(upi_payment), 0) as upi,
                COALESCE(SUM(old_gold_adjustment), 0) as old_gold
            ')
            ->first();

        $paymentMethods = $paymentMethodsQuery ? (object) [
            'cash' => $paymentMethodsQuery->cash ?? 0,
            'card' => $paymentMethodsQuery->card ?? 0,
            'upi' => $paymentMethodsQuery->upi ?? 0,
            'old_gold' => $paymentMethodsQuery->old_gold ?? 0,
        ] : (object) [
            'cash' => 0,
            'card' => 0,
            'upi' => 0,
            'old_gold' => 0,
        ];

        return view('reports.sales', compact(
            'salesSummary',
            'salesByPeriod',
            'topCustomers',
            'paymentMethods',
            'fromDate',
            'toDate',
            'groupBy'
        ));
    }

    /**
     * Inventory reports
     */
    public function inventoryReport(Request $request)
    {
        $category = $request->get('category');
        $metalType = $request->get('metal_type');
        $status = $request->get('status');

        $query = Product::query();

        if ($category) {
            $query->where('category', $category);
        }
        if ($metalType) {
            $query->where('metal_type', $metalType);
        }
        if ($status) {
            $query->where('status', $status);
        }

        // Inventory summary
        $inventorySummary = [
            'total_products' => $query->count(),
            'total_value' => $query->sum(DB::raw('selling_price * quantity')),
            'low_stock_items' => Product::where('quantity', '<=', 5)->count(),
            'out_of_stock_items' => Product::where('quantity', 0)->count(),
        ];

        // Products by category
        $productsByCategory = Product::select('category', DB::raw('COUNT(*) as count'), DB::raw('SUM(selling_price * quantity) as value'))
            ->groupBy('category')
            ->get();

        // Products by metal type
        $productsByMetal = Product::select('metal_type', DB::raw('COUNT(*) as count'), DB::raw('SUM(selling_price * quantity) as value'))
            ->whereNotNull('metal_type')
            ->groupBy('metal_type')
            ->get();

        // Low stock items
        $lowStockItems = Product::where('quantity', '<=', 5)
            ->orderBy('quantity')
            ->limit(20)
            ->get();

        return view('reports.inventory', compact(
            'inventorySummary',
            'productsByCategory',
            'productsByMetal',
            'lowStockItems',
            'category',
            'metalType',
            'status'
        ));
    }

    /**
     * Customer reports
     */
    public function customerReport(Request $request)
    {
        $fromDate = $request->get('from_date', now()->startOfYear()->format('Y-m-d'));
        $toDate = $request->get('to_date', now()->format('Y-m-d'));

        // Customer summary
        $customerSummary = [
            'total_customers' => Customer::count(),
            'new_customers' => Customer::whereBetween('created_at', [$fromDate, $toDate])->count(),
            'active_customers' => Customer::whereHas('sales', function($q) use ($fromDate, $toDate) {
                $q->whereBetween('sale_date', [$fromDate, $toDate]);
            })->count(),
            'customers_with_schemes' => Customer::whereHas('savingSchemes')->count(),
        ];

        // Top customers by purchase value
        $topCustomers = Customer::select('customers.*', DB::raw('SUM(sales.total_amount) as total_spent'), DB::raw('COUNT(sales.id) as order_count'))
            ->leftJoin('sales', 'customers.id', '=', 'sales.customer_id')
            ->whereBetween('sales.sale_date', [$fromDate, $toDate])
            ->groupBy('customers.id')
            ->orderBy('total_spent', 'desc')
            ->limit(20)
            ->get();

        // Customer acquisition by month
        $customerAcquisition = Customer::select(
                DB::raw("strftime('%Y', created_at) as year"),
                DB::raw("strftime('%m', created_at) as month"),
                DB::raw('COUNT(*) as count')
            )
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();

        return view('reports.customers', compact(
            'customerSummary',
            'topCustomers',
            'customerAcquisition',
            'fromDate',
            'toDate'
        ));
    }

    /**
     * Repair reports
     */
    public function repairReport(Request $request)
    {
        $fromDate = $request->get('from_date', now()->startOfMonth()->format('Y-m-d'));
        $toDate = $request->get('to_date', now()->format('Y-m-d'));

        // Repair summary
        $repairSummary = [
            'total_repairs' => Repair::whereBetween('received_date', [$fromDate, $toDate])->count(),
            'completed_repairs' => Repair::whereBetween('received_date', [$fromDate, $toDate])->where('status', 'completed')->count(),
            'delivered_repairs' => Repair::whereBetween('received_date', [$fromDate, $toDate])->where('status', 'delivered')->count(),
            'overdue_repairs' => Repair::overdue()->count(),
            'total_revenue' => Repair::whereBetween('received_date', [$fromDate, $toDate])->sum('actual_charges'),
            'average_repair_time' => $this->getAverageRepairTime($fromDate, $toDate),
        ];

        // Repairs by status
        $repairsByStatus = Repair::select('status', DB::raw('COUNT(*) as count'))
            ->whereBetween('received_date', [$fromDate, $toDate])
            ->groupBy('status')
            ->get();

        // Overdue repairs
        $overdueRepairs = Repair::overdue()
            ->with(['customer'])
            ->orderBy('promised_date')
            ->limit(20)
            ->get();

        return view('reports.repairs', compact(
            'repairSummary',
            'repairsByStatus',
            'overdueRepairs',
            'fromDate',
            'toDate'
        ));
    }

    /**
     * Saving scheme reports
     */
    public function schemeReport(Request $request)
    {
        $fromDate = $request->get('from_date', now()->startOfYear()->format('Y-m-d'));
        $toDate = $request->get('to_date', now()->format('Y-m-d'));

        // Scheme summary
        $schemeSummary = [
            'total_schemes' => SavingScheme::whereBetween('start_date', [$fromDate, $toDate])->count(),
            'active_schemes' => SavingScheme::active()->count(),
            'matured_schemes' => SavingScheme::matured()->count(),
            'total_collections' => SavingScheme::sum('total_paid'),
            'pending_collections' => SavingScheme::active()->get()->sum('pending_amount'),
            'overdue_schemes' => SavingScheme::overdue()->count(),
        ];

        // Schemes by type
        $schemesByType = SavingScheme::select('scheme_name', DB::raw('COUNT(*) as count'), DB::raw('SUM(total_paid) as collections'))
            ->groupBy('scheme_name')
            ->get();

        // Monthly collections
        $monthlyCollections = SavingScheme::select(
                DB::raw("strftime('%Y', start_date) as year"),
                DB::raw("strftime('%m', start_date) as month"),
                DB::raw('SUM(total_paid) as collections')
            )
            ->whereBetween('start_date', [$fromDate, $toDate])
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();

        return view('reports.schemes', compact(
            'schemeSummary',
            'schemesByType',
            'monthlyCollections',
            'fromDate',
            'toDate'
        ));
    }

    /**
     * Financial reports
     */
    public function financialReport(Request $request)
    {
        $fromDate = $request->get('from_date', now()->startOfMonth()->format('Y-m-d'));
        $toDate = $request->get('to_date', now()->format('Y-m-d'));

        // Revenue breakdown
        $revenue = [
            'sales_revenue' => Sale::whereBetween('sale_date', [$fromDate, $toDate])->sum('total_amount'),
            'repair_revenue' => Repair::whereBetween('received_date', [$fromDate, $toDate])->sum('actual_charges'),
            'scheme_collections' => SavingScheme::whereBetween('start_date', [$fromDate, $toDate])->sum('total_paid'),
        ];

        // Tax summary
        $taxSummary = [
            'total_tax_collected' => Sale::whereBetween('sale_date', [$fromDate, $toDate])->sum('total_tax'),
            'cgst_collected' => Sale::whereBetween('sale_date', [$fromDate, $toDate])->sum('cgst_amount'),
            'sgst_collected' => Sale::whereBetween('sale_date', [$fromDate, $toDate])->sum('sgst_amount'),
        ];

        // Payment method analysis
        $paymentAnalysisQuery = Sale::whereBetween('sale_date', [$fromDate, $toDate])
            ->selectRaw('
                COALESCE(SUM(cash_payment), 0) as cash_total,
                COALESCE(SUM(card_payment), 0) as card_total,
                COALESCE(SUM(upi_payment), 0) as upi_total,
                COALESCE(SUM(old_gold_adjustment), 0) as old_gold_total,
                COUNT(CASE WHEN cash_payment > 0 THEN 1 END) as cash_count,
                COUNT(CASE WHEN card_payment > 0 THEN 1 END) as card_count,
                COUNT(CASE WHEN upi_payment > 0 THEN 1 END) as upi_count,
                COUNT(CASE WHEN old_gold_adjustment > 0 THEN 1 END) as old_gold_count
            ')
            ->first();

        $paymentAnalysis = $paymentAnalysisQuery ? (object) [
            'cash_total' => $paymentAnalysisQuery->cash_total ?? 0,
            'card_total' => $paymentAnalysisQuery->card_total ?? 0,
            'upi_total' => $paymentAnalysisQuery->upi_total ?? 0,
            'old_gold_total' => $paymentAnalysisQuery->old_gold_total ?? 0,
            'cash_count' => $paymentAnalysisQuery->cash_count ?? 0,
            'card_count' => $paymentAnalysisQuery->card_count ?? 0,
            'upi_count' => $paymentAnalysisQuery->upi_count ?? 0,
            'old_gold_count' => $paymentAnalysisQuery->old_gold_count ?? 0,
        ] : (object) [
            'cash_total' => 0,
            'card_total' => 0,
            'upi_total' => 0,
            'old_gold_total' => 0,
            'cash_count' => 0,
            'card_count' => 0,
            'upi_count' => 0,
            'old_gold_count' => 0,
        ];

        // Calculate total revenue from different sources
        $salesRevenue = Sale::whereBetween('sale_date', [$fromDate, $toDate])->sum('total_amount');
        $repairRevenue = Repair::whereBetween('received_date', [$fromDate, $toDate])->sum('actual_charges');
        $schemeRevenue = SchemePayment::whereBetween('payment_date', [$fromDate, $toDate])->sum('amount');
        
        // Total revenue and expenses
        $totalRevenue = $salesRevenue + $repairRevenue + $schemeRevenue;
        $totalExpenses = DB::table('expenses') // Assuming you have an expenses table
            ->whereBetween('date', [$fromDate, $toDate])
            ->sum('amount') ?? 0;
        
        // Calculate net profit and margin
        $netProfit = $totalRevenue - $totalExpenses;
        $profitMargin = $totalRevenue > 0 ? ($netProfit / $totalRevenue) * 100 : 0;

        // Prepare financial summary
        $financialSummary = [
            'total_revenue' => $totalRevenue,
            'total_expenses' => $totalExpenses,
            'net_profit' => $netProfit,
            'profit_margin' => $profitMargin
        ];

        // Prepare revenue breakdown
        $revenueBreakdown = [
            [
                'source' => 'Sales',
                'amount' => $salesRevenue,
                'percentage' => $totalRevenue > 0 ? ($salesRevenue / $totalRevenue) * 100 : 0
            ],
            [
                'source' => 'Repairs',
                'amount' => $repairRevenue,
                'percentage' => $totalRevenue > 0 ? ($repairRevenue / $totalRevenue) * 100 : 0
            ],
            [
                'source' => 'Savings Schemes',
                'amount' => $schemeRevenue,
                'percentage' => $totalRevenue > 0 ? ($schemeRevenue / $totalRevenue) * 100 : 0
            ]
        ];

        // Get monthly summary
        $monthlySummary = DB::table('sales')
            ->select(
                DB::raw("strftime('%Y-%m', sale_date) as period"),
                DB::raw('SUM(total_amount) as revenue'),
                DB::raw('0 as expenses'), // You'll need to join with expenses table
                DB::raw('SUM(total_amount) as profit'),
                DB::raw('100 as margin')
            )
            ->whereBetween('sale_date', [$fromDate, $toDate])
            ->groupBy('period')
            ->orderBy('period', 'asc')
            ->get()
            ->map(function($item) {
                $item->expenses = 0; // Replace with actual expenses calculation
                $item->profit = $item->revenue - $item->expenses;
                $item->margin = $item->revenue > 0 ? ($item->profit / $item->revenue) * 100 : 0;
                return $item;
            });

        return view('reports.financial', compact(
            'financialSummary',
            'revenueBreakdown',
            'monthlySummary',
            'fromDate',
            'toDate'
        ));
    }

    /**
     * Export report to PDF
     */
    public function exportPdf(Request $request)
    {
        $reportType = $request->get('report_type');
        $fromDate = $request->get('from_date');
        $toDate = $request->get('to_date');

        switch ($reportType) {
            case 'sales':
                $data = $this->getSalesReportData($fromDate, $toDate);
                $view = 'reports.pdf.sales';
                break;
            case 'inventory':
                $data = $this->getInventoryReportData();
                $view = 'reports.pdf.inventory';
                break;
            case 'customers':
                $data = $this->getCustomerReportData($fromDate, $toDate);
                $view = 'reports.pdf.customers';
                break;
            default:
                return redirect()->back()->with('error', 'Invalid report type');
        }

        $pdf = Pdf::loadView($view, compact('data', 'fromDate', 'toDate'));
        return $pdf->download($reportType . '-report-' . date('Y-m-d') . '.pdf');
    }

    /**
     * Helper methods
     */
    private function getSalesByPeriod($fromDate, $toDate, $groupBy)
    {
        $dateFormat = match($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'year' => '%Y',
            default => '%Y-%m-%d'
        };

    // Use strftime for SQLite compatibility
    return Sale::select(
        DB::raw("strftime('{$dateFormat}', sale_date) as period"),
        DB::raw('SUM(total_amount) as total_sales'),
        DB::raw('COUNT(*) as order_count')
        )
        ->whereBetween('sale_date', [$fromDate, $toDate])
        ->groupBy('period')
        ->orderBy('period')
        ->get();
    }

    private function getAverageRepairTime($fromDate, $toDate)
    {
        $repairs = Repair::whereBetween('received_date', [$fromDate, $toDate])
            ->whereNotNull('completed_date')
            ->get();

        if ($repairs->isEmpty()) {
            return 0;
        }

        $totalDays = $repairs->sum(function ($repair) {
            return $repair->received_date->diffInDays($repair->completed_date);
        });

        return round($totalDays / $repairs->count(), 1);
    }

    private function getSalesReportData($fromDate, $toDate)
    {
        return [
            'sales' => Sale::with(['customer', 'saleItems.product'])
                ->whereBetween('sale_date', [$fromDate, $toDate])
                ->orderBy('sale_date', 'desc')
                ->get(),
            'summary' => [
                'total_sales' => Sale::whereBetween('sale_date', [$fromDate, $toDate])->sum('total_amount'),
                'total_orders' => Sale::whereBetween('sale_date', [$fromDate, $toDate])->count(),
                'total_tax' => Sale::whereBetween('sale_date', [$fromDate, $toDate])->sum('total_tax'),
            ]
        ];
    }

    private function getInventoryReportData()
    {
        return [
            'products' => Product::orderBy('category', 'name')->get(),
            'summary' => [
                'total_products' => Product::count(),
                'total_value' => Product::sum(DB::raw('selling_price * quantity')),
                'low_stock_count' => Product::where('quantity', '<=', 5)->count(),
            ]
        ];
    }

    private function getCustomerReportData($fromDate, $toDate)
    {
        return [
            'customers' => Customer::with(['sales' => function($q) use ($fromDate, $toDate) {
                $q->whereBetween('sale_date', [$fromDate, $toDate]);
            }])->get(),
            'summary' => [
                'total_customers' => Customer::count(),
                'new_customers' => Customer::whereBetween('created_at', [$fromDate, $toDate])->count(),
            ]
        ];
    }
}
