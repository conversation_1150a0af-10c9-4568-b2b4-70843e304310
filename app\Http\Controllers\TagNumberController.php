<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Services\TagNumberService;
use Illuminate\Support\Facades\DB;

class TagNumberController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:manage_inventory')->only(['index', 'generateMissing', 'regenerate']);
    }

    /**
     * Display tag number management interface
     */
    public function index(Request $request)
    {
        $query = Product::query();

        // Filter by tag number status
        if ($request->filled('tag_status')) {
            if ($request->tag_status === 'with_tag') {
                $query->whereNotNull('tag_number');
            } elseif ($request->tag_status === 'without_tag') {
                $query->whereNull('tag_number');
            }
        }

        // Filter by category prefix
        if ($request->filled('tag_prefix')) {
            $query->where('tag_prefix', $request->tag_prefix);
        }

        // Filter by year
        if ($request->filled('tag_year')) {
            $year = substr($request->tag_year, -2); // Get last 2 digits
            $query->where('tag_number', 'LIKE', '__' . $year . '%');
        }

        $products = $query->latest()->paginate(20);

        // Get statistics
        $stats = [
            'total_products' => Product::count(),
            'with_tag_numbers' => Product::whereNotNull('tag_number')->count(),
            'without_tag_numbers' => Product::whereNull('tag_number')->count(),
        ];

        // Get available prefixes and years
        $prefixes = Product::whereNotNull('tag_prefix')
            ->distinct()
            ->pluck('tag_prefix')
            ->sort();

        $years = Product::whereNotNull('tag_number')
            ->get()
            ->map(function ($product) {
                $parsed = TagNumberService::parseTagNumber($product->tag_number);
                return $parsed['valid'] ? $parsed['full_year'] : null;
            })
            ->filter()
            ->unique()
            ->sort()
            ->values();

        return view('tag-numbers.index', compact('products', 'stats', 'prefixes', 'years'));
    }

    /**
     * Generate tag numbers for products without them
     */
    public function generateMissing()
    {
        try {
            $count = TagNumberService::generateMissingTagNumbers();
            
            return redirect()->route('tag-numbers.index')
                ->with('success', "Generated tag numbers for {$count} products");
        } catch (\Exception $e) {
            return redirect()->route('tag-numbers.index')
                ->with('error', 'Error generating tag numbers: ' . $e->getMessage());
        }
    }

    /**
     * Regenerate tag number for a specific product
     */
    public function regenerate(Product $product)
    {
        try {
            $tagNumber = TagNumberService::generateTagNumber(
                $product->category ?? 'other',
                $product->metal_type ?? 'gold',
                $product->purity ?? '22K'
            );
            
            $parsed = TagNumberService::parseTagNumber($tagNumber);
            
            $product->update([
                'tag_number' => $tagNumber,
                'tag_prefix' => $parsed['prefix'],
                'tag_sequence' => $parsed['sequence'],
                'tag_suffix' => $parsed['suffix']
            ]);
            
            return redirect()->route('tag-numbers.index')
                ->with('success', "Tag number regenerated for {$product->name}: {$tagNumber}");
        } catch (\Exception $e) {
            return redirect()->route('tag-numbers.index')
                ->with('error', 'Error regenerating tag number: ' . $e->getMessage());
        }
    }

    /**
     * Show tag number statistics and analytics
     */
    public function analytics()
    {
        // Get statistics by category
        $categoryStats = Product::whereNotNull('tag_prefix')
            ->select('tag_prefix', DB::raw('count(*) as count'))
            ->groupBy('tag_prefix')
            ->orderBy('count', 'desc')
            ->get();

        // Get statistics by year
        $yearStats = Product::whereNotNull('tag_number')
            ->get()
            ->groupBy(function ($product) {
                $parsed = TagNumberService::parseTagNumber($product->tag_number);
                return $parsed['valid'] ? $parsed['full_year'] : 'Invalid';
            })
            ->map(function ($group) {
                return $group->count();
            })
            ->sortKeys();

        // Get statistics by metal type
        $metalStats = Product::whereNotNull('tag_suffix')
            ->select('tag_suffix', DB::raw('count(*) as count'))
            ->groupBy('tag_suffix')
            ->orderBy('count', 'desc')
            ->get();

        // Get recent tag numbers
        $recentTagNumbers = Product::whereNotNull('tag_number')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('tag-numbers.analytics', compact(
            'categoryStats',
            'yearStats', 
            'metalStats',
            'recentTagNumbers'
        ));
    }

    /**
     * Validate tag number format
     */
    public function validate(Request $request)
    {
        $request->validate([
            'tag_number' => 'required|string|max:20'
        ]);

        $validation = TagNumberService::validateTagNumber($request->tag_number);
        
        return response()->json($validation);
    }

    /**
     * Search products by tag number
     */
    public function search(Request $request)
    {
        $request->validate([
            'tag_number' => 'required|string'
        ]);

        $product = Product::where('tag_number', $request->tag_number)->first();

        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found with this tag number'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'category' => $product->category,
                'metal_type' => $product->metal_type,
                'purity' => $product->purity,
                'tag_number' => $product->tag_number,
                'formatted_tag_number' => $product->formatted_tag_number,
                'selling_price' => $product->selling_price,
                'status' => $product->status,
                'url' => route('products.show', $product)
            ]
        ]);
    }

    /**
     * Export tag numbers to CSV
     */
    public function export()
    {
        $products = Product::whereNotNull('tag_number')
            ->select('tag_number', 'name', 'category', 'metal_type', 'purity', 'selling_price', 'status')
            ->get();

        $filename = 'tag-numbers-' . date('Y-m-d-H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($products) {
            $file = fopen('php://output', 'w');
            
            // Add CSV headers
            fputcsv($file, ['Tag Number', 'Product Name', 'Category', 'Metal Type', 'Purity', 'Price', 'Status']);
            
            // Add data rows
            foreach ($products as $product) {
                fputcsv($file, [
                    $product->tag_number,
                    $product->name,
                    $product->category,
                    $product->metal_type,
                    $product->purity,
                    $product->selling_price,
                    $product->status
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
