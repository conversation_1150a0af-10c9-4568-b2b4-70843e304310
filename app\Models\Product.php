<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;
use App\Services\TagNumberService;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'category_id',
        'category',
        'metal_type',
        'purity',
        'gross_weight',
        'net_weight',
        'stone_weight',
        'wastage_percentage',
        'making_charges',
        'stone_charges',
        'hsn_code',
        'huid_number',
        'huid_required',
        'ahc_code',
        'hallmark_date',
        'bis_logo_number',
        'hallmark_charges',
        'huid_compliance_notes',
        'tag_number',
        'tag_prefix',
        'tag_sequence',
        'tag_suffix',
        'barcode',
        'cost_price',
        'selling_price',
        'quantity',
        'size',
        'description',
        'image_path',
        'has_stones',
        'stone_details',
        'status',
    ];

    protected $casts = [
        'gross_weight' => 'decimal:3',
        'net_weight' => 'decimal:3',
        'stone_weight' => 'decimal:3',
        'wastage_percentage' => 'decimal:2',
        'making_charges' => 'decimal:2',
        'stone_charges' => 'decimal:2',
        'huid_required' => 'boolean',
        'hallmark_date' => 'date',
        'hallmark_charges' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'has_stones' => 'boolean',
        'stone_details' => 'array',
    ];

    // Relationships
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function saleItems()
    {
        return $this->hasMany(SaleItem::class);
    }

    // Accessors & Mutators
    public function getProfitAttribute()
    {
        return $this->selling_price - $this->cost_price;
    }

    public function getProfitMarginAttribute()
    {
        if ($this->cost_price == 0) return 0;
        return (($this->selling_price - $this->cost_price) / $this->cost_price) * 100;
    }

    public function getWastageAmountAttribute()
    {
        return ($this->net_weight * $this->wastage_percentage) / 100;
    }

    public function getTotalWeightAttribute()
    {
        return $this->net_weight + $this->wastage_amount;
    }

    // Scopes
    public function scopeInStock($query)
    {
        return $query->where('status', 'in_stock');
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByMetalType($query, $metalType)
    {
        return $query->where('metal_type', $metalType);
    }

    public function scopeWithStones($query)
    {
        return $query->where('has_stones', true);
    }

    // Boot method to generate barcode and tag number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            // Generate barcode if not provided
            if (empty($product->barcode)) {
                $product->barcode = 'JP' . strtoupper(Str::random(8));
            }

            // Generate tag number if not provided
            if (empty($product->tag_number)) {
                $tagNumber = TagNumberService::generateTagNumber(
                    $product->category ?? 'other',
                    $product->metal_type ?? 'gold',
                    $product->purity ?? '22K'
                );

                $parsed = TagNumberService::parseTagNumber($tagNumber);
                $product->tag_number = $tagNumber;
                $product->tag_prefix = $parsed['prefix'];
                $product->tag_sequence = $parsed['sequence'];
                $product->tag_suffix = $parsed['suffix'];
            }
        });
    }

    /**
     * Generate unique barcode
     */
    public static function generateUniqueBarcode()
    {
        do {
            // Generate 13-digit barcode starting with 2 (for internal use)
            $barcode = '2' . str_pad(mt_rand(0, 999999999999), 12, '0', STR_PAD_LEFT);
        } while (static::where('barcode', $barcode)->exists());

        return $barcode;
    }

    /**
     * Generate unique tag number
     */
    public static function generateUniqueTagNumber($category, $metalType, $purity)
    {
        return TagNumberService::generateTagNumber($category, $metalType, $purity);
    }

    /**
     * Scope to search by tag number
     */
    public function scopeByTagNumber($query, $tagNumber)
    {
        return $query->where('tag_number', $tagNumber);
    }

    /**
     * Scope to filter by tag prefix (category)
     */
    public function scopeByTagPrefix($query, $prefix)
    {
        return $query->where('tag_prefix', $prefix);
    }

    /**
     * Get formatted tag number display
     */
    public function getFormattedTagNumberAttribute()
    {
        if (!$this->tag_number) {
            return 'Not Assigned';
        }

        $parsed = TagNumberService::parseTagNumber($this->tag_number);
        if (!$parsed['valid']) {
            return $this->tag_number;
        }

        return $parsed['prefix'] . '-' . $parsed['year'] . '-' .
               str_pad($parsed['sequence'], 6, '0', STR_PAD_LEFT) . '-' . $parsed['suffix'];
    }

    /**
     * Get tag number year
     */
    public function getTagYearAttribute()
    {
        if (!$this->tag_number) {
            return null;
        }

        $parsed = TagNumberService::parseTagNumber($this->tag_number);
        return $parsed['valid'] ? $parsed['full_year'] : null;
    }
}
