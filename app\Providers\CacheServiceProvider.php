<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Cache;
use App\Models\MetalRate;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Support\Facades\View;

class CacheServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Cache metal rates for 15 minutes
        $this->cacheMetalRates();

        // Cache categories for 1 hour
        $this->cacheCategories();

        // Cache popular products for 30 minutes
        $this->cachePopularProducts();

        // Share cached data with views
        $this->shareWithViews();
    }

    protected function cacheMetalRates(): void
    {
        Cache::remember('metal_rates', now()->addMinutes(15), function () {
            return MetalRate::latest()->take(5)->get();
        });
    }

    protected function cacheCategories(): void
    {
        Cache::remember('categories', now()->addHour(), function () {
            return Category::with('products')->get();
        });
    }

    protected function cachePopularProducts(): void
    {
        Cache::remember('popular_products', now()->addMinutes(30), function () {
            return Product::withCount('sales')
                ->orderBy('sales_count', 'desc')
                ->take(10)
                ->get();
        });
    }

    protected function shareWithViews(): void
    {
        View::composer(['dashboard', 'products.*'], function ($view) {
            $view->with([
                'metalRates' => Cache::get('metal_rates'),
                'popularProducts' => Cache::get('popular_products'),
            ]);
        });

        View::composer(['products.*', 'categories.*'], function ($view) {
            $view->with('categories', Cache::get('categories'));
        });
    }
}
