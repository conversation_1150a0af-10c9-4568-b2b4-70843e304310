<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Services\HuidValidationService;

class HuidRequired implements ValidationRule
{
    protected $metalType;
    protected $netWeight;

    public function __construct(string $metalType, float $netWeight)
    {
        $this->metalType = $metalType;
        $this->netWeight = $netWeight;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if HUID is required for this item
        if (HuidValidationService::isHuidRequired($this->metalType, $this->netWeight)) {
            if (empty($value)) {
                $message = HuidValidationService::getComplianceMessage($this->metalType, $this->netWeight);
                $fail($message);
            }
        }
    }
}
