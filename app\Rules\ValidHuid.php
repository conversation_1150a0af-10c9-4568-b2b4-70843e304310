<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Services\HuidValidationService;

class ValidHuid implements ValidationRule
{
    protected $metalType;
    protected $netWeight;
    protected $required;

    public function __construct(string $metalType = null, float $netWeight = null, bool $required = false)
    {
        $this->metalType = $metalType;
        $this->netWeight = $netWeight;
        $this->required = $required;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // If value is empty
        if (empty($value)) {
            // Check if HUID is required based on metal type and weight
            if ($this->required || ($this->metalType && $this->netWeight && HuidValidationService::isHuidRequired($this->metalType, $this->netWeight))) {
                $fail('HUID is mandatory for this jewelry item as per BIS regulations.');
            }
            return;
        }

        // Validate HUID format
        $validation = HuidValidationService::validateHuidFormat($value);
        
        if (!$validation['valid']) {
            $fail('Invalid HUID format: ' . implode(', ', $validation['errors']));
        }
    }
}
