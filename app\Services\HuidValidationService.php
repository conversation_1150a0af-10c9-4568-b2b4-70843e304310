<?php

namespace App\Services;

class HuidValidationService
{
    /**
     * Check if HUID is required for a jewelry item
     * As per BIS regulations, HUID is mandatory for gold jewelry above 2 grams
     */
    public static function isHuidRequired(string $metalType, float $netWeight, string $purity = null): bool
    {
        // HUID is mandatory for gold jewelry above 2 grams
        if (strtolower($metalType) === 'gold' && $netWeight > 2.0) {
            return true;
        }
        
        // HUID is also required for silver jewelry above 5 grams (as per latest BIS guidelines)
        if (strtolower($metalType) === 'silver' && $netWeight > 5.0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Validate HUID format as per BIS standards
     * Format: 6 alphanumeric characters (A-Z, 0-9)
     * Examples: A00001, Z99999, M1N2O3, X7Y8Z9
     */
    public static function validateHuidFormat(string $huid): array
    {
        $result = [
            'valid' => false,
            'errors' => [],
            'huid' => null
        ];

        // Remove any spaces and convert to uppercase
        $huid = strtoupper(trim($huid));

        // Check length
        if (strlen($huid) !== 6) {
            $result['errors'][] = 'HUID must be exactly 6 characters';
            return $result;
        }

        // Check if all characters are alphanumeric (A-Z, 0-9)
        if (!ctype_alnum($huid)) {
            $result['errors'][] = 'HUID must contain only letters (A-Z) and numbers (0-9)';
            return $result;
        }

        // Validate format using regex
        if (!preg_match('/^[A-Z0-9]{6}$/', $huid)) {
            $result['errors'][] = 'HUID format is invalid';
            return $result;
        }

        // Additional validation: should not be all zeros
        if ($huid === '000000') {
            $result['errors'][] = 'HUID cannot be all zeros';
            return $result;
        }

        $result['valid'] = true;
        $result['huid'] = $huid;

        return $result;
    }
    
    /**
     * Get compliance message for HUID requirement
     */
    public static function getComplianceMessage(string $metalType, float $netWeight): string
    {
        if (self::isHuidRequired($metalType, $netWeight)) {
            return "HUID is mandatory for {$metalType} jewelry above " . 
                   (strtolower($metalType) === 'gold' ? '2 grams' : '5 grams') . 
                   " as per BIS regulations.";
        }
        
        return "HUID is not mandatory for this item but recommended for authenticity.";
    }
    
    /**
     * Validate HUID compliance for billing
     */
    public static function validateBillingCompliance(array $items): array
    {
        $errors = [];
        $warnings = [];
        
        foreach ($items as $index => $item) {
            $itemNumber = $index + 1;
            $metalType = $item['metal_type'] ?? '';
            $netWeight = (float) ($item['net_weight'] ?? 0);
            $huidNumber = $item['huid_number'] ?? '';
            
            // Check if HUID is required
            if (self::isHuidRequired($metalType, $netWeight)) {
                if (empty($huidNumber)) {
                    $errors[] = "Item #{$itemNumber}: HUID is mandatory for {$metalType} jewelry above " . 
                               (strtolower($metalType) === 'gold' ? '2g' : '5g') . " (Current: {$netWeight}g)";
                } else {
                    // Validate HUID format
                    $validation = self::validateHuidFormat($huidNumber);
                    if (!$validation['valid']) {
                        $errors[] = "Item #{$itemNumber}: Invalid HUID format - " . implode(', ', $validation['errors']);
                    }
                }
            } else {
                if (!empty($huidNumber)) {
                    // Validate format even if not required
                    $validation = self::validateHuidFormat($huidNumber);
                    if (!$validation['valid']) {
                        $warnings[] = "Item #{$itemNumber}: HUID format is invalid - " . implode(', ', $validation['errors']);
                    }
                }
            }
        }
        
        return [
            'compliant' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        ];
    }
    
    /**
     * Get list of valid AHC codes (sample - in real implementation, this would come from BIS database)
     */
    public static function getValidAhcCodes(): array
    {
        return [
            '100001' => 'Mumbai AHC',
            '100002' => 'Delhi AHC', 
            '100003' => 'Kolkata AHC',
            '100004' => 'Chennai AHC',
            '100005' => 'Bangalore AHC',
            '100006' => 'Hyderabad AHC',
            '100007' => 'Pune AHC',
            '100008' => 'Ahmedabad AHC',
            '100009' => 'Surat AHC',
            '100010' => 'Jaipur AHC',
            // Add more AHC codes as needed
        ];
    }
    
    /**
     * Format HUID for display
     */
    public static function formatHuidForDisplay(string $huid): string
    {
        $huid = preg_replace('/[^0-9]/', '', $huid);
        
        if (strlen($huid) === 16) {
            return substr($huid, 0, 6) . '-' . substr($huid, 6, 10);
        }
        
        return $huid;
    }
}
