<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\DB;

class TagNumberService
{
    /**
     * Category prefixes for Indian jewellery
     */
    private static $categoryPrefixes = [
        'ring' => 'RG',
        'necklace' => 'NK',
        'earrings' => 'ER',
        'bracelet' => 'BR',
        'bangle' => 'BG',
        'pendant' => 'PD',
        'chain' => 'CH',
        'anklet' => 'AN',
        'maang tikka' => 'MT',
        'nose ring' => 'NS',
        'choker' => 'CK',
        'armlet' => 'AM',
        'toe ring' => 'TR',
        'waist chain' => 'WC',
        'hair ornament' => 'HO',
        'brooch' => 'BC',
        'cufflinks' => 'CL',
        'watch' => 'WH',
        'other' => 'OT'
    ];

    /**
     * Metal type suffixes
     */
    private static $metalSuffixes = [
        'gold' => [
            '24K' => 'G24', '22K' => 'G22', '21K' => 'G21', '20K' => 'G20',
            '18K' => 'G18', '16K' => 'G16', '14K' => 'G14', '12K' => 'G12',
            '10K' => 'G10', '9K' => 'G09', '8K' => 'G08'
        ],
        'silver' => [
            '999' => 'S999', '925' => 'S925', '900' => 'S900', '800' => 'S800'
        ],
        'platinum' => [
            '950' => 'PT950', '900' => 'PT900', '850' => 'PT850'
        ],
        'white gold' => [
            '18K' => 'WG18', '14K' => 'WG14', '10K' => 'WG10'
        ],
        'rose gold' => [
            '18K' => 'RG18', '14K' => 'RG14', '10K' => 'RG10'
        ]
    ];

    /**
     * Generate a unique tag number for a product
     */
    public static function generateTagNumber(string $category, string $metalType, string $purity): string
    {
        $prefix = self::getCategoryPrefix($category);
        $year = date('y'); // 2-digit year
        $suffix = self::getMetalSuffix($metalType, $purity);
        
        // Get next sequence number for this prefix and year
        $sequence = self::getNextSequence($prefix, $year);
        
        // Format: {PREFIX}{YEAR}{SEQUENCE}{SUFFIX}
        $tagNumber = $prefix . $year . str_pad($sequence, 6, '0', STR_PAD_LEFT) . $suffix;
        
        // Ensure uniqueness
        while (Product::where('tag_number', $tagNumber)->exists()) {
            $sequence++;
            $tagNumber = $prefix . $year . str_pad($sequence, 6, '0', STR_PAD_LEFT) . $suffix;
        }
        
        return $tagNumber;
    }

    /**
     * Get category prefix from category name
     */
    public static function getCategoryPrefix(string $category): string
    {
        $categoryLower = strtolower(trim($category));
        
        // Direct match
        if (isset(self::$categoryPrefixes[$categoryLower])) {
            return self::$categoryPrefixes[$categoryLower];
        }
        
        // Partial match
        foreach (self::$categoryPrefixes as $key => $prefix) {
            if (str_contains($categoryLower, $key) || str_contains($key, $categoryLower)) {
                return $prefix;
            }
        }
        
        return 'OT'; // Other
    }

    /**
     * Get metal suffix from metal type and purity
     */
    public static function getMetalSuffix(string $metalType, string $purity): string
    {
        $metalTypeLower = strtolower(trim($metalType));
        $purityClean = strtoupper(trim($purity));
        
        // Remove common prefixes/suffixes from purity
        $purityClean = str_replace(['K', 'KT', 'KARAT'], 'K', $purityClean);
        
        if (isset(self::$metalSuffixes[$metalTypeLower][$purityClean])) {
            return self::$metalSuffixes[$metalTypeLower][$purityClean];
        }
        
        // Fallback logic
        if (str_contains($metalTypeLower, 'gold')) {
            $purityNumber = preg_replace('/[^0-9]/', '', $purity);
            if ($purityNumber) {
                return 'G' . str_pad($purityNumber, 2, '0', STR_PAD_LEFT);
            }
            return 'G22'; // Default gold
        }
        
        if (str_contains($metalTypeLower, 'silver')) {
            return 'S925'; // Default silver
        }
        
        if (str_contains($metalTypeLower, 'platinum')) {
            return 'PT950'; // Default platinum
        }
        
        return 'MT'; // Mixed/Other metal
    }

    /**
     * Get next sequence number for a prefix and year
     */
    private static function getNextSequence(string $prefix, string $year): int
    {
        $maxSequence = Product::where('tag_prefix', $prefix)
            ->where('tag_number', 'LIKE', $prefix . $year . '%')
            ->max('tag_sequence');
        
        return ($maxSequence ?? 0) + 1;
    }

    /**
     * Parse tag number into components
     */
    public static function parseTagNumber(string $tagNumber): array
    {
        if (strlen($tagNumber) < 10) {
            return ['valid' => false, 'error' => 'Tag number too short'];
        }
        
        $prefix = substr($tagNumber, 0, 2);
        $year = substr($tagNumber, 2, 2);
        $sequence = substr($tagNumber, 4, 6);
        $suffix = substr($tagNumber, 10);
        
        return [
            'valid' => true,
            'prefix' => $prefix,
            'year' => $year,
            'sequence' => (int)$sequence,
            'suffix' => $suffix,
            'full_year' => '20' . $year
        ];
    }

    /**
     * Validate tag number format
     */
    public static function validateTagNumber(string $tagNumber): array
    {
        $parsed = self::parseTagNumber($tagNumber);
        
        if (!$parsed['valid']) {
            return $parsed;
        }
        
        // Check if prefix is valid
        if (!in_array($parsed['prefix'], array_values(self::$categoryPrefixes))) {
            return ['valid' => false, 'error' => 'Invalid category prefix'];
        }
        
        // Check year format
        if (!is_numeric($parsed['year']) || strlen($parsed['year']) !== 2) {
            return ['valid' => false, 'error' => 'Invalid year format'];
        }
        
        // Check sequence format
        if (!is_numeric(substr($tagNumber, 4, 6))) {
            return ['valid' => false, 'error' => 'Invalid sequence format'];
        }
        
        return ['valid' => true, 'parsed' => $parsed];
    }

    /**
     * Get all available category prefixes
     */
    public static function getCategoryPrefixes(): array
    {
        return self::$categoryPrefixes;
    }

    /**
     * Get all available metal suffixes
     */
    public static function getMetalSuffixes(): array
    {
        return self::$metalSuffixes;
    }

    /**
     * Generate tag numbers for existing products without tag numbers
     */
    public static function generateMissingTagNumbers(): int
    {
        $products = Product::whereNull('tag_number')->get();
        $count = 0;
        
        foreach ($products as $product) {
            $tagNumber = self::generateTagNumber(
                $product->category ?? 'other',
                $product->metal_type ?? 'gold',
                $product->purity ?? '22K'
            );
            
            $parsed = self::parseTagNumber($tagNumber);
            
            $product->update([
                'tag_number' => $tagNumber,
                'tag_prefix' => $parsed['prefix'],
                'tag_sequence' => $parsed['sequence'],
                'tag_suffix' => $parsed['suffix']
            ]);
            
            $count++;
        }
        
        return $count;
    }
}
