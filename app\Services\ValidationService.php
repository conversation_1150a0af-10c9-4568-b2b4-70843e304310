<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ValidationService
{
    /**
     * Validate a sale transaction
     */
    public function validateSale(array $data): array
    {
        return Validator::make($data, [
            'customer_id' => 'required|exists:customers,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'payment_method' => 'required|in:cash,card,upi',
            'discount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500'
        ])->validate();
    }

    /**
     * Validate product creation/update
     */
    public function validateProduct(array $data, ?Product $product = null): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'sku' => 'required|string|max:50|unique:products,sku' . ($product ? ','.$product->id : ''),
            'price' => 'required|numeric|min:0',
            'stock' => 'required|integer|min:0',
            'weight' => 'required|numeric|min:0',
            'metal_type' => 'required|in:gold,silver,platinum',
            'purity' => 'required|numeric|between:0,100',
            'making_charge' => 'required|numeric|min:0',
            'images.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'description' => 'nullable|string|max:1000',
            'specifications' => 'nullable|array',
            'specifications.*.name' => 'required|string|max:100',
            'specifications.*.value' => 'required|string|max:255'
        ];

        return Validator::make($data, $rules)->validate();
    }

    /**
     * Validate customer creation/update
     */
    public function validateCustomer(array $data): array
    {
        return Validator::make($data, [
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:customers,email',
            'phone' => ['required', 'string', 'regex:/^([0-9\s\-\+\(\)]*)$/', 'min:10'],
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'pincode' => 'required|string|size:6',
            'gstin' => 'nullable|string|size:15',
            'date_of_birth' => 'nullable|date',
            'anniversary_date' => 'nullable|date'
        ])->validate();
    }

    /**
     * Validate metal rate update
     */
    public function validateMetalRate(array $data): array
    {
        return Validator::make($data, [
            'metal_type' => 'required|in:gold,silver,platinum',
            'purity' => 'required|numeric|between:0,100',
            'rate' => 'required|numeric|min:0',
            'effective_date' => 'required|date'
        ])->validate();
    }
}
