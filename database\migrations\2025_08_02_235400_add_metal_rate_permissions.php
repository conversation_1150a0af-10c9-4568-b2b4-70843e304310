<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create new granular metal rate permissions
        $permissions = [
            'view_metal_rates',
            'create_metal_rate',
            'edit_metal_rate',
            'delete_metal_rate',
            'toggle_metal_rate_status',
            'bulk_manage_metal_rates',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to Admin role
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole) {
            $adminRole->givePermissionTo($permissions);
        }

        // Assign view permission to Cashier and Staff roles
        $cashierRole = Role::where('name', 'Cashier')->first();
        if ($cashierRole) {
            $cashierRole->givePermissionTo(['view_metal_rates']);
        }

        $staffRole = Role::where('name', 'Staff')->first();
        if ($staffRole) {
            $staffRole->givePermissionTo(['view_metal_rates']);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $permissions = [
            'view_metal_rates',
            'create_metal_rate',
            'edit_metal_rate',
            'delete_metal_rate',
            'toggle_metal_rate_status',
            'bulk_manage_metal_rates',
        ];

        foreach ($permissions as $permission) {
            Permission::where('name', $permission)->delete();
        }
    }
};
