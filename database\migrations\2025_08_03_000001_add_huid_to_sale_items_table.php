<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sale_items', function (Blueprint $table) {
            $table->string('huid_number', 6)->nullable()->after('product_id');
            $table->boolean('huid_required')->default(false)->after('huid_number');
            $table->text('huid_compliance_notes')->nullable()->after('huid_required');
            
            // Add index for HUID lookups
            $table->index('huid_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sale_items', function (Blueprint $table) {
            $table->dropIndex(['huid_number']);
            $table->dropColumn(['huid_number', 'huid_required', 'huid_compliance_notes']);
        });
    }
};
