<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Enhance HUID field
            $table->string('huid_number', 6)->nullable()->change();
            
            // Add HUID compliance fields
            $table->boolean('huid_required')->default(false)->after('huid_number');
            $table->string('ahc_code', 6)->nullable()->after('huid_required'); // Assaying and Hallmarking Centre code
            $table->date('hallmark_date')->nullable()->after('ahc_code');
            $table->string('bis_logo_number', 10)->nullable()->after('hallmark_date');
            $table->decimal('hallmark_charges', 8, 2)->default(0)->after('bis_logo_number');
            $table->text('huid_compliance_notes')->nullable()->after('hallmark_charges');
            
            // Add indexes
            $table->index('ahc_code');
            $table->index('hallmark_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['ahc_code']);
            $table->dropIndex(['hallmark_date']);
            $table->dropColumn([
                'huid_required', 
                'ahc_code', 
                'hallmark_date', 
                'bis_logo_number', 
                'hallmark_charges',
                'huid_compliance_notes'
            ]);
        });
    }
};
