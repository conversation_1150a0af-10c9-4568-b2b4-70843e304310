<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update HUID length from 16 to 6 characters in all tables
        Schema::table('products', function (Blueprint $table) {
            $table->string('huid_number', 6)->nullable()->change();
        });

        Schema::table('sale_items', function (Blueprint $table) {
            $table->string('huid_number', 6)->nullable()->change();
        });

        Schema::table('estimate_items', function (Blueprint $table) {
            $table->string('huid_number', 6)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert HUID length back to 16 characters
        Schema::table('products', function (Blueprint $table) {
            $table->string('huid_number', 16)->nullable()->change();
        });

        Schema::table('sale_items', function (Blueprint $table) {
            $table->string('huid_number', 16)->nullable()->change();
        });

        Schema::table('estimate_items', function (Blueprint $table) {
            $table->string('huid_number', 16)->nullable()->change();
        });
    }
};
