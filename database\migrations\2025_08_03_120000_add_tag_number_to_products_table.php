<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('tag_number', 20)->nullable()->unique()->after('huid_number');
            $table->string('tag_prefix', 5)->nullable()->after('tag_number'); // Category prefix (RG, NK, etc.)
            $table->integer('tag_sequence')->nullable()->after('tag_prefix'); // Sequence number for the year
            $table->string('tag_suffix', 10)->nullable()->after('tag_sequence'); // Metal and purity suffix
            
            // Add indexes for efficient lookups
            $table->index('tag_number');
            $table->index(['tag_prefix', 'tag_sequence']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['tag_number']);
            $table->dropIndex(['tag_prefix', 'tag_sequence']);
            $table->dropColumn(['tag_number', 'tag_prefix', 'tag_sequence', 'tag_suffix']);
        });
    }
};
