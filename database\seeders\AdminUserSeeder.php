<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        if (!$admin->hasRole('Admin')) {
            $admin->assignRole('Admin');
        }

        // Create sample users for other roles
        $cashier = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Cashier User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );
        if (!$cashier->hasRole('Cashier')) {
            $cashier->assignRole('Cashier');
        }

        $staff = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Staff User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );
        if (!$staff->hasRole('Staff')) {
            $staff->assignRole('Staff');
        }

        $accountant = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Accountant User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );
        if (!$accountant->hasRole('Accountant')) {
            $accountant->assignRole('Accountant');
        }
    }
}
