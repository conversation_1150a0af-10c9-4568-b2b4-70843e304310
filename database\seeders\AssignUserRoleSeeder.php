<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;

class AssignUserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Find the first user or create one
        $user = User::first();

        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
            ]);
        }

        // Assign Admin role to the user
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole && !$user->hasRole('Admin')) {
            $user->assignRole($adminRole);
            $this->command->info("Assigned Admin role to user: {$user->email}");
        }
    }
}
