<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Main Categories
        $categories = [
            [
                'name' => 'Rings',
                'description' => 'All types of rings including engagement, wedding, and fashion rings',
                'sort_order' => 1,
                'subcategories' => [
                    ['name' => 'Gold Rings', 'description' => 'Traditional gold rings', 'sort_order' => 1],
                    ['name' => 'Diamond Rings', 'description' => 'Diamond studded rings', 'sort_order' => 2],
                    ['name' => 'Silver Rings', 'description' => 'Sterling silver rings', 'sort_order' => 3],
                ]
            ],
            [
                'name' => 'Necklaces',
                'description' => 'Necklaces and chains of various styles and lengths',
                'sort_order' => 2,
                'subcategories' => [
                    ['name' => 'Gold Chains', 'description' => 'Traditional gold chains', 'sort_order' => 1],
                    ['name' => 'Pearl Necklaces', 'description' => 'Pearl studded necklaces', 'sort_order' => 2],
                    ['name' => 'Diamond Necklaces', 'description' => 'Diamond studded necklaces', 'sort_order' => 3],
                ]
            ],
            [
                'name' => 'Earrings',
                'description' => 'Earrings including studs, hoops, and danglers',
                'sort_order' => 3,
                'subcategories' => [
                    ['name' => 'Gold Earrings', 'description' => 'Traditional gold earrings', 'sort_order' => 1],
                    ['name' => 'Diamond Earrings', 'description' => 'Diamond studded earrings', 'sort_order' => 2],
                    ['name' => 'Pearl Earrings', 'description' => 'Pearl studded earrings', 'sort_order' => 3],
                ]
            ],
            [
                'name' => 'Bracelets',
                'description' => 'Bracelets and bangles for wrists',
                'sort_order' => 4,
                'subcategories' => [
                    ['name' => 'Gold Bracelets', 'description' => 'Traditional gold bracelets', 'sort_order' => 1],
                    ['name' => 'Silver Bracelets', 'description' => 'Sterling silver bracelets', 'sort_order' => 2],
                    ['name' => 'Diamond Bracelets', 'description' => 'Diamond studded bracelets', 'sort_order' => 3],
                ]
            ],
            [
                'name' => 'Pendants',
                'description' => 'Pendants and lockets',
                'sort_order' => 5,
                'subcategories' => [
                    ['name' => 'Gold Pendants', 'description' => 'Traditional gold pendants', 'sort_order' => 1],
                    ['name' => 'Diamond Pendants', 'description' => 'Diamond studded pendants', 'sort_order' => 2],
                    ['name' => 'Religious Pendants', 'description' => 'Religious and spiritual pendants', 'sort_order' => 3],
                ]
            ],
            [
                'name' => 'Bangles',
                'description' => 'Traditional bangles and kada',
                'sort_order' => 6,
                'subcategories' => [
                    ['name' => 'Gold Bangles', 'description' => 'Traditional gold bangles', 'sort_order' => 1],
                    ['name' => 'Silver Bangles', 'description' => 'Sterling silver bangles', 'sort_order' => 2],
                    ['name' => 'Diamond Bangles', 'description' => 'Diamond studded bangles', 'sort_order' => 3],
                ]
            ],
        ];

        foreach ($categories as $categoryData) {
            $subcategories = $categoryData['subcategories'] ?? [];
            unset($categoryData['subcategories']);

            $category = Category::create($categoryData);

            // Create subcategories
            foreach ($subcategories as $subcategoryData) {
                $subcategoryData['parent_id'] = $category->id;
                Category::create($subcategoryData);
            }
        }
    }
}
