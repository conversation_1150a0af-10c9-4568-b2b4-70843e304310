<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Customer;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 3 sample customers
        Customer::factory()->create([
            'name' => '<PERSON><PERSON>',
            'mobile' => '9876543210',
            'email' => '<EMAIL>',
            'address' => '123 MG Road',
            'city' => 'Mumbai',
            'state' => 'Maharashtra',
            'pincode' => '400001',
            'kyc_type' => 'Aadhar',
            'kyc_number' => '1234-5678-9012',
            'is_active' => true,
        ]);

        Customer::factory()->create([
            'name' => 'Priya Sharma',
            'mobile' => '9876543211',
            'email' => '<EMAIL>',
            'address' => '456 Brigade Road',
            'city' => 'Bangalore',
            'state' => 'Karnataka',
            'pincode' => '560001',
            'kyc_type' => 'PAN',
            'kyc_number' => '**********',
            'is_active' => true,
        ]);

        Customer::factory()->create([
            'name' => 'Amit Patel',
            'mobile' => '**********',
            'email' => '<EMAIL>',
            'address' => '789 CG Road',
            'city' => 'Ahmedabad',
            'state' => 'Gujarat',
            'pincode' => '380001',
            'kyc_type' => 'Driving License',
            'kyc_number' => 'GJ0120110012345',
            'is_active' => true,
        ]);
    }
}
