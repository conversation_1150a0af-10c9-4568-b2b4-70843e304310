<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MetalRate;
use App\Models\User;

class MetalRateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::where('email', '<EMAIL>')->first();

        if (!$admin) {
            return;
        }

        $rates = [
            // Sample rates for testing
            ['metal_type' => 'Gold', 'purity' => '22K', 'rate_per_gram' => 6200.00],
            ['metal_type' => 'Silver', 'purity' => '925', 'rate_per_gram' => 85.00],
            ['metal_type' => 'Gold', 'purity' => '18K', 'rate_per_gram' => 5100.00],
        ];

        foreach ($rates as $rate) {
            MetalRate::create([
                'metal_type' => $rate['metal_type'],
                'purity' => $rate['purity'],
                'rate_per_gram' => $rate['rate_per_gram'],
                'rate_per_10_gram' => $rate['rate_per_gram'] * 10,
                'effective_date' => today(),
                'is_active' => true,
                'created_by' => $admin->id,
            ]);
        }
    }
}
