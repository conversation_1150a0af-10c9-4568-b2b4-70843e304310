<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create 3 sample products with specific HUID examples
        Product::factory()->create([
            'name' => 'Gold Ring - Classic Design',
            'category' => 'Ring',
            'metal_type' => 'Gold',
            'purity' => '22K',
            'gross_weight' => 5.250,
            'net_weight' => 4.800,
            'huid_number' => 'A00001',
            'huid_required' => true,
            'status' => 'in_stock',
        ]);

        Product::factory()->create([
            'name' => 'Silver Necklace - Traditional',
            'category' => 'Necklace',
            'metal_type' => 'Silver',
            'purity' => '925',
            'gross_weight' => 12.500,
            'net_weight' => 11.200,
            'huid_number' => 'Z99999',
            'huid_required' => true,
            'status' => 'in_stock',
        ]);

        Product::factory()->create([
            'name' => 'Diamond Earrings - Premium',
            'category' => 'Earrings',
            'metal_type' => 'Gold',
            'purity' => '18K',
            'gross_weight' => 3.750,
            'net_weight' => 3.200,
            'stone_weight' => 0.550,
            'has_stones' => true,
            'huid_number' => 'M1N2O3',
            'huid_required' => true,
            'status' => 'in_stock',
            'stone_details' => [
                'type' => 'Diamond',
                'clarity' => 'VS1',
                'color' => 'D'
            ],
        ]);
    }
}
