<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Dashboard
            'view_dashboard',

            // Inventory
            'view_inventory', 'create_product', 'edit_product', 'delete_product',
            'manage_barcode', 'view_stock_report',

            // Sales & Billing
            'create_sale', 'edit_sale', 'delete_sale', 'view_sales',
            'print_invoice', 'export_invoice', 'edit_metal_rates',

            // Old Gold
            'create_old_gold_purchase', 'edit_old_gold_purchase', 'view_old_gold_purchases',

            // Estimates
            'create_estimate', 'edit_estimate', 'delete_estimate', 'view_estimates',
            'convert_estimate_to_sale',

            // Repairs
            'create_repair', 'edit_repair', 'view_repairs', 'update_repair_status',

            // Customers
            'create_customer', 'edit_customer', 'view_customers', 'delete_customer',

            // Saving Schemes
            'create_scheme', 'edit_scheme', 'view_schemes', 'manage_scheme_payments',

            // Reports
            'view_sales_report', 'view_gst_report', 'view_profit_report',
            'view_customer_report', 'export_reports',

            // Settings
            'manage_settings', 'manage_users', 'manage_roles',
            'manage_metal_rates', 'manage_tax_settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $admin = Role::firstOrCreate(['name' => 'Admin']);
        $admin->givePermissionTo(Permission::all());

        $cashier = Role::firstOrCreate(['name' => 'Cashier']);
        $cashier->givePermissionTo([
            'view_dashboard', 'create_sale', 'view_sales', 'print_invoice',
            'create_customer', 'view_customers', 'view_inventory',
            'create_old_gold_purchase', 'view_old_gold_purchases',
            'create_estimate', 'view_estimates', 'convert_estimate_to_sale',
        ]);

        $staff = Role::firstOrCreate(['name' => 'Staff']);
        $staff->givePermissionTo([
            'view_dashboard', 'view_inventory', 'view_customers',
            'create_repair', 'edit_repair', 'view_repairs', 'update_repair_status',
            'view_estimates', 'view_sales',
        ]);

        $accountant = Role::firstOrCreate(['name' => 'Accountant']);
        $accountant->givePermissionTo([
            'view_dashboard', 'view_sales', 'view_sales_report',
            'view_gst_report', 'view_profit_report', 'view_customer_report',
            'export_reports', 'view_inventory', 'view_customers',
        ]);
    }
}
