@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for JewelBill */
@layer base {
    body {
        font-family: 'Inter', sans-serif;
    }
}

@layer components {
    /* Sidebar styles */
    .sidebar-nav-group {
        @apply pt-4;
    }

    .sidebar-nav-group-title {
        @apply px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider;
    }

    /* Card styles */
    .card {
        @apply bg-white overflow-hidden shadow-sm sm:rounded-lg;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
    }

    .card-body {
        @apply p-6;
    }

    /* Button styles */
    .btn {
        @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
    }

    .btn-primary {
        @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
    }

    .btn-secondary {
        @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
    }

    .btn-success {
        @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
    }

    .btn-danger {
        @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
    }

    .btn-outline {
        @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-indigo-500;
    }

    /* Form styles */
    .form-input {
        @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
    }

    .form-select {
        @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
    }

    .form-textarea {
        @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
    }

    /* Table styles */
    .table {
        @apply min-w-full divide-y divide-gray-200;
    }

    .table-header {
        @apply bg-gray-50;
    }

    .table-header-cell {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    .table-body {
        @apply bg-white divide-y divide-gray-200;
    }

    .table-cell {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }

    /* Status badges */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-success {
        @apply bg-green-100 text-green-800;
    }

    .badge-warning {
        @apply bg-yellow-100 text-yellow-800;
    }

    .badge-danger {
        @apply bg-red-100 text-red-800;
    }

    .badge-info {
        @apply bg-blue-100 text-blue-800;
    }

    .badge-gray {
        @apply bg-gray-100 text-gray-800;
    }
}

@layer utilities {
    /* Custom scrollbar */
    .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: rgb(156 163 175) transparent;
    }

    .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
        background: transparent;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgb(156 163 175);
        border-radius: 3px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
        background-color: rgb(107 114 128);
    }
}
