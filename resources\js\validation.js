import Alpine from 'alpinejs';

document.addEventListener('alpine:init', () => {
    Alpine.data('formValidation', () => ({
        errors: {},
        loading: false,

        validateProduct(form) {
            this.errors = {};
            this.loading = true;

            // Basic client-side validation
            if (!form.name.value) {
                this.errors.name = 'Product name is required';
            }

            if (!form.price.value || form.price.value < 0) {
                this.errors.price = 'Please enter a valid price';
            }

            if (!form.stock.value || form.stock.value < 0) {
                this.errors.stock = 'Please enter a valid stock quantity';
            }

            if (!form.weight.value || form.weight.value < 0) {
                this.errors.weight = 'Please enter a valid weight';
            }

            if (!form.category_id.value) {
                this.errors.category_id = 'Please select a category';
            }

            // If there are errors, prevent form submission
            if (Object.keys(this.errors).length > 0) {
                this.loading = false;
                return false;
            }

            return true;
        },

        validateCustomer(form) {
            this.errors = {};
            this.loading = true;

            const phoneRegex = /^([0-9\s\-\+\(\)]*)$/;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (!form.name.value) {
                this.errors.name = 'Customer name is required';
            }

            if (form.email.value && !emailRegex.test(form.email.value)) {
                this.errors.email = 'Please enter a valid email address';
            }

            if (!form.phone.value || !phoneRegex.test(form.phone.value)) {
                this.errors.phone = 'Please enter a valid phone number';
            }

            if (!form.address.value) {
                this.errors.address = 'Address is required';
            }

            if (!form.pincode.value || form.pincode.value.length !== 6) {
                this.errors.pincode = 'Please enter a valid 6-digit pincode';
            }

            if (Object.keys(this.errors).length > 0) {
                this.loading = false;
                return false;
            }

            return true;
        },

        validateSale(form) {
            this.errors = {};
            this.loading = true;

            if (!form.customer_id.value) {
                this.errors.customer_id = 'Please select a customer';
            }

            if (!form.items || form.items.length === 0) {
                this.errors.items = 'Please add at least one item to the sale';
            }

            if (!form.payment_method.value) {
                this.errors.payment_method = 'Please select a payment method';
            }

            if (Object.keys(this.errors).length > 0) {
                this.loading = false;
                return false;
            }

            return true;
        }
    }));
});
