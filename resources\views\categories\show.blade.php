<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="{{ route('categories.index') }}" class="text-gray-600 hover:text-gray-900 mr-4">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Category Details') }}
                </h2>
            </div>
        </div>
    </x-slot>
<div class="container mx-auto px-4 py-6">
    <div class="max-w-4xl mx-auto">
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
                <a href="{{ route('categories.index') }}" class="text-gray-600 hover:text-gray-900 mr-4">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $category->name }}</h1>
                    <p class="text-gray-600 mt-1">Category Details</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                @can('edit_category')
                <a href="{{ route('categories.edit', $category) }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                </a>
                @endcan
                @can('toggle_category_status')
                <form method="POST" action="{{ route('categories.toggle-active', $category) }}" class="inline">
                    @csrf
                    <button type="submit" 
                            class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                        </svg>
                        {{ $category->is_active ? 'Deactivate' : 'Activate' }}
                    </button>
                </form>
                @endcan
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Information -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Category Information</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Name</label>
                            <p class="mt-1 text-lg text-gray-900">{{ $category->name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Type</label>
                            <div class="mt-1">
                                @if($category->isParent())
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                        Parent Category
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                        Subcategory
                                    </span>
                                @endif
                            </div>
                        </div>

                        @if($category->parent)
                        <div>
                            <label class="block text-sm font-medium text-gray-500">Parent Category</label>
                            <p class="mt-1 text-lg text-gray-900">{{ $category->parent->name }}</p>
                        </div>
                        @endif

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Status</label>
                            <div class="mt-1">
                                @if($category->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                        Inactive
                                    </span>
                                @endif
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Sort Order</label>
                            <p class="mt-1 text-lg text-gray-900">{{ $category->sort_order }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-500">Products Count</label>
                            <p class="mt-1 text-lg text-gray-900">{{ $productsCount }}</p>
                        </div>
                    </div>

                    @if($category->description)
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-500">Description</label>
                        <p class="mt-1 text-gray-900">{{ $category->description }}</p>
                    </div>
                    @endif
                </div>

                <!-- Subcategories -->
                @if($category->hasChildren())
                <div class="bg-white rounded-lg shadow-sm border p-6 mt-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Subcategories</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach($category->children as $child)
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-medium text-gray-900">{{ $child->name }}</h3>
                                    @if($child->description)
                                    <p class="text-sm text-gray-500 mt-1">{{ Str::limit($child->description, 50) }}</p>
                                    @endif
                                    <p class="text-xs text-gray-400 mt-1">{{ $child->products->count() }} products</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($child->is_active)
                                        <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                                    @else
                                        <span class="w-2 h-2 bg-red-400 rounded-full"></span>
                                    @endif
                                    @can('view_categories')
                                    <a href="{{ route('categories.show', $child) }}" class="text-indigo-600 hover:text-indigo-900 text-sm">View</a>
                                    @endcan
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Recent Products -->
                @if($category->products->count() > 0)
                <div class="bg-white rounded-lg shadow-sm border p-6 mt-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-900">Recent Products</h2>
                        <a href="{{ route('products.index', ['category' => $category->name]) }}" class="text-indigo-600 hover:text-indigo-900 text-sm">View All</a>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach($category->products->take(4) as $product)
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center">
                                @if($product->image_path)
                                <img src="{{ Storage::url($product->image_path) }}" alt="{{ $product->name }}" class="w-12 h-12 rounded-lg object-cover mr-3">
                                @else
                                <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                </div>
                                @endif
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900">{{ $product->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ $product->metal_type }} - {{ $product->purity }}</p>
                                    <p class="text-sm text-gray-400">₹{{ number_format($product->selling_price, 2) }}</p>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Category Image -->
                @if($category->image_path)
                <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Category Image</h3>
                    <img src="{{ Storage::url($category->image_path) }}" alt="{{ $category->name }}" class="w-full h-48 object-cover rounded-lg">
                </div>
                @endif

                <!-- Statistics -->
                <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Total Products</span>
                            <span class="font-medium">{{ $productsCount }}</span>
                        </div>
                        @if($category->hasChildren())
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subcategories</span>
                            <span class="font-medium">{{ $category->children->count() }}</span>
                        </div>
                        @endif
                        <div class="flex justify-between">
                            <span class="text-gray-600">Created</span>
                            <span class="font-medium">{{ $category->created_at->format('M d, Y') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Last Updated</span>
                            <span class="font-medium">{{ $category->updated_at->format('M d, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm border p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        @can('create_category')
                        <a href="{{ route('categories.create') }}?parent_id={{ $category->id }}" 
                           class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-center block">
                            Add Subcategory
                        </a>
                        @endcan
                        <a href="{{ route('products.index', ['category' => $category->name]) }}" 
                           class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-center block">
                            View Products
                        </a>
                        @can('create_product')
                        <a href="{{ route('products.create') }}?category_id={{ $category->id }}" 
                           class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-center block">
                            Add Product
                        </a>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-app-layout>
