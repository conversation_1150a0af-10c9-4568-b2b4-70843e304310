@props([
    'title',
    'description' => null,
    'actions' => null,
    'breadcrumbs' => null,
    'icon' => null
])

<header {{ $attributes->merge(['class' => 'bg-white shadow']) }}>
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        @if($breadcrumbs)
            <nav class="text-sm mb-2">
                <ol class="list-none p-0 inline-flex text-gray-500">
                    {{ $breadcrumbs }}
                </ol>
            </nav>
        @endif
        
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <div class="flex items-center">
                    @if($icon)
                        <div class="flex-shrink-0 text-gray-500 mr-4">
                            {{ $icon }}
                        </div>
                    @endif
                    <div>
                        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                            {{ $title }}
                        </h2>
                        @if($description)
                            <p class="mt-1 text-sm text-gray-500">
                                {{ $description }}
                            </p>
                        @endif
                    </div>
                </div>
            </div>
            @if($actions)
                <div class="mt-4 flex-shrink-0 flex md:mt-0 md:ml-4 space-x-3">
                    {{ $actions }}
                </div>
            @endif
        </div>
    </div>
</header>
