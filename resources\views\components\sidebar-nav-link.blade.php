@props([
    'active' => false,
    'icon' => null,
    'badge' => null,
    'collapsed' => false
])

@php
$classes = ($active ?? false)
            ? 'bg-indigo-100 border-indigo-500 text-indigo-700 group flex items-center px-2 py-2 text-sm font-medium border-l-4'
            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium border-l-4 border-transparent hover:border-gray-300';
@endphp

<a {{ $attributes->merge(['class' => $classes]) }}>
    @if($icon)
        <x-dynamic-component :component="'icons.' . $icon" class="mr-3 flex-shrink-0 h-5 w-5 {{ $active ? 'text-indigo-500' : 'text-gray-400 group-hover:text-gray-500' }}" />
    @endif
    
    <span class="{{ $collapsed ? 'sr-only' : '' }} flex-1">{{ $slot }}</span>
    
    @if($badge && !$collapsed)
        <span class="ml-3 inline-block py-0.5 px-2 text-xs font-medium rounded-full {{ $active ? 'bg-indigo-200 text-indigo-800' : 'bg-gray-100 text-gray-600' }}">
            {{ $badge }}
        </span>
    @endif
</a>
