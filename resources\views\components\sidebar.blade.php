@props(['collapsed' => false])

<div x-data="{
    collapsed: $persist(false).as('sidebarCollapsed'),
    activeDropdown: null,
    toggleDropdown(menu) {
        this.activeDropdown = this.activeDropdown === menu ? null : menu;
    }
}" class="flex" x-cloak>
    <!-- Sidebar -->
    <div :class="collapsed ? 'w-16' : 'w-64'" class="bg-white shadow-lg h-screen fixed left-0 top-0 z-40 transition-all duration-300 ease-in-out">
        <!-- Logo and Toggle -->
        <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <div :class="collapsed ? 'hidden' : 'flex'" class="items-center">
                <x-application-logo class="h-8 w-auto" />
                <span class="ml-2 text-xl font-semibold text-gray-900">JewelBill</span>
            </div>
            <button @click="collapsed = !collapsed" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500">
                <x-icons.menu x-show="collapsed" class="h-5 w-5" />
                <x-icons.close x-show="!collapsed" class="h-5 w-5" />
            </button>
        </div>

        <!-- Navigation -->
        <nav class="mt-5 px-2 space-y-1 overflow-y-auto h-full pb-20">
            <!-- Dashboard -->
            <x-sidebar-nav-link :href="route('dashboard')" :active="request()->routeIs('dashboard')" icon="dashboard" :collapsed="$collapsed">
                Dashboard
            </x-sidebar-nav-link>

            <!-- Main Operations -->
            <div class="pt-4">
                <button @click="toggleDropdown('operations')" 
                        class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
                        x-bind:class="activeDropdown === 'operations' ? 'bg-gray-50' : ''">
                    <x-icons.shop class="w-5 h-5 mr-3" />
                    <span x-bind:class="collapsed ? 'sr-only' : ''">Operations</span>
                    <x-icons.chevron-down class="w-4 h-4 ml-auto transition-transform"
                        x-bind:class="[activeDropdown === 'operations' ? 'transform rotate-180' : '', collapsed ? 'sr-only' : '']" />
                </button>
                
                <div x-show="!collapsed && activeDropdown === 'operations'" 
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     class="mt-2 space-y-1 pl-11">
                    
                    <x-sidebar-nav-link :href="route('sales.index')" :active="request()->routeIs('sales.*')" icon="sales" :collapsed="$collapsed">
                        Sales
                    </x-sidebar-nav-link>
                    <x-sidebar-nav-link :href="route('estimates.index')" :active="request()->routeIs('estimates.*')" icon="estimates" :collapsed="$collapsed">
                        Estimates
                    </x-sidebar-nav-link>
                    <x-sidebar-nav-link :href="route('old-gold-purchases.index')" :active="request()->routeIs('old-gold-purchases.*')" icon="old-gold" :collapsed="$collapsed">
                        Old Gold
                    </x-sidebar-nav-link>
                </div>
            </div>

            <!-- Inventory -->
            <div class="pt-4">
                <button @click="toggleDropdown('inventory')" 
                        class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
                        x-bind:class="activeDropdown === 'inventory' ? 'bg-gray-50' : ''">
                    <x-icons.inventory class="w-5 h-5 mr-3" />
                    <span x-bind:class="collapsed ? 'sr-only' : ''">Inventory</span>
                    <x-icons.chevron-down class="w-4 h-4 ml-auto transition-transform"
                        x-bind:class="[activeDropdown === 'inventory' ? 'transform rotate-180' : '', collapsed ? 'sr-only' : '']" />
                </button>
                
                <div x-show="!collapsed && activeDropdown === 'inventory'" 
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     class="mt-2 space-y-1 pl-11">
                    
                    @can('view_categories')
                    <x-sidebar-nav-link :href="route('categories.index')" :active="request()->routeIs('categories.*')" icon="categories" :collapsed="$collapsed">
                        Categories
                    </x-sidebar-nav-link>
                    @endcan
                    <x-sidebar-nav-link :href="route('products.index')" :active="request()->routeIs('products.*')" icon="inventory" :collapsed="$collapsed">
                        Products
                    </x-sidebar-nav-link>
                    <x-sidebar-nav-link :href="route('barcodes.index')" :active="request()->routeIs('barcodes.*')" icon="barcode" :collapsed="$collapsed">
                        Barcodes
                    </x-sidebar-nav-link>
                </div>
            </div>

            <!-- Customer & Services -->
            <div class="pt-4">
                <button @click="toggleDropdown('customers')" 
                        class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
                        x-bind:class="activeDropdown === 'customers' ? 'bg-gray-50' : ''">
                    <x-icons.users class="w-5 h-5 mr-3" />
                    <span x-bind:class="collapsed ? 'sr-only' : ''">Customers & Services</span>
                    <x-icons.chevron-down class="w-4 h-4 ml-auto transition-transform"
                        x-bind:class="[activeDropdown === 'customers' ? 'transform rotate-180' : '', collapsed ? 'sr-only' : '']" />
                </button>
                
                <div x-show="!collapsed && activeDropdown === 'customers'" 
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     class="mt-2 space-y-1 pl-11">
                    
                    <x-sidebar-nav-link :href="route('customers.index')" :active="request()->routeIs('customers.*')" icon="customers" :collapsed="$collapsed">
                        Customers
                    </x-sidebar-nav-link>
                    <x-sidebar-nav-link :href="route('repairs.index')" :active="request()->routeIs('repairs.*')" icon="repairs" :collapsed="$collapsed">
                        Repairs
                    </x-sidebar-nav-link>
                    <x-sidebar-nav-link :href="route('saving-schemes.index')" :active="request()->routeIs('saving-schemes.*')" icon="saving-schemes" :collapsed="$collapsed">
                        Saving Schemes
                    </x-sidebar-nav-link>
                </div>
            </div>

            <!-- Tools & Reports -->
            <div class="pt-4">
                <button @click="toggleDropdown('tools')" 
                        class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
                        x-bind:class="activeDropdown === 'tools' ? 'bg-gray-50' : ''">
                    <x-icons.tools class="w-5 h-5 mr-3" />
                    <span x-bind:class="collapsed ? 'sr-only' : ''">Tools & Reports</span>
                    <x-icons.chevron-down class="w-4 h-4 ml-auto transition-transform"
                        x-bind:class="[activeDropdown === 'tools' ? 'transform rotate-180' : '', collapsed ? 'sr-only' : '']" />
                </button>
                
                <div x-show="!collapsed && activeDropdown === 'tools'" 
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     class="mt-2 space-y-1 pl-11">
                    
                    <x-sidebar-nav-link :href="route('metal-rates.index')" :active="request()->routeIs('metal-rates.*')" icon="metal-rates" :collapsed="$collapsed">
                        Metal Rates
                    </x-sidebar-nav-link>
                    <x-sidebar-nav-link :href="route('calculator')" :active="request()->routeIs('calculator*')" icon="calculator" :collapsed="$collapsed">
                        Calculator
                    </x-sidebar-nav-link>
                    <x-sidebar-nav-link :href="route('reports.index')" :active="request()->routeIs('reports.*')" icon="reports" :collapsed="$collapsed">
                        Reports
                    </x-sidebar-nav-link>
                </div>
            </div>

            <!-- Settings -->
            <div class="pt-4">
                <x-sidebar-nav-link :href="route('settings.index')" :active="request()->routeIs('settings.*')" icon="settings" :collapsed="$collapsed">
                    Settings
                </x-sidebar-nav-link>
            </div>
        </nav>
    </div>

    <!-- Mobile overlay -->
    <div x-show="!collapsed" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 z-30 bg-gray-600 opacity-50 lg:hidden" @click="collapsed = true"></div>
</div>
