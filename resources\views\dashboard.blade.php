<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Dashboard') }}
            </h2>
            <div class="text-sm text-gray-500">
                Welcome back, {{ Auth::user()->name }}
            </div>
        </div>
    </x-slot>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 md:gap-6 mb-8">
        <div class="card transform transition-transform duration-300 hover:scale-105 hover:shadow-lg">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <x-icons.customers class="w-6 h-6 text-white" />
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-500">Total Customers</div>
                        <div class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_customers']) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card transform transition-transform duration-300 hover:scale-105 hover:shadow-lg">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <x-icons.inventory class="w-6 h-6 text-white" />
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-500">Products in Stock</div>
                        <div class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_products']) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card transform transition-transform duration-300 hover:scale-105 hover:shadow-lg">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                            <x-icons.sales class="w-6 h-6 text-white" />
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-500">Today's Sales</div>
                        <div class="text-2xl font-bold text-gray-900">₹{{ number_format($stats['today_sales'], 2) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card transform transition-transform duration-300 hover:scale-105 hover:shadow-lg">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                            <x-icons.estimates class="w-6 h-6 text-white" />
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-500">Pending Estimates</div>
                        <div class="text-2xl font-bold text-gray-900">{{ number_format($stats['pending_estimates']) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card transform transition-transform duration-300 hover:scale-105 hover:shadow-lg">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                            <x-icons.repairs class="w-6 h-6 text-white" />
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-500">Pending Repairs</div>
                        <div class="text-2xl font-bold text-gray-900">{{ number_format($stats['pending_repairs']) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Saving Scheme Statistics -->
    <div class="card mb-8 hover:shadow-lg transition-shadow duration-300">
        <div class="card-header bg-gradient-to-r from-indigo-50 to-blue-50">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Saving Schemes</h3>
                <a href="{{ route('saving-schemes.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 transition-colors duration-200 flex items-center">
                    View all
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-6">
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transform transition-transform duration-300 hover:scale-105">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <x-icons.saving-schemes class="w-5 h-5 text-blue-600" />
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">Active Schemes</div>
                            <div class="text-xl font-bold text-gray-900">{{ number_format($stats['active_schemes']) }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transform transition-transform duration-300 hover:scale-105">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                <x-icons.saving-schemes class="w-5 h-5 text-green-600" />
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">Matured Schemes</div>
                            <div class="text-xl font-bold text-gray-900">{{ number_format($stats['matured_schemes']) }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transform transition-transform duration-300 hover:scale-105">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                <x-icons.saving-schemes class="w-5 h-5 text-red-600" />
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">Overdue Schemes</div>
                            <div class="text-xl font-bold text-gray-900">{{ number_format($stats['overdue_schemes']) }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transform transition-transform duration-300 hover:scale-105">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                <x-icons.calculator class="w-5 h-5 text-indigo-600" />
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">Total Collections</div>
                            <div class="text-xl font-bold text-gray-900">₹{{ number_format($stats['total_collections'], 2) }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100 transform transition-transform duration-300 hover:scale-105">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <x-icons.calculator class="w-5 h-5 text-yellow-600" />
                            </div>
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-gray-500">Pending Collections</div>
                            <div class="text-xl font-bold text-gray-900">₹{{ number_format($stats['pending_collections'], 2) }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Sales Chart -->
    <div class="card mb-8 hover:shadow-lg transition-shadow duration-300">
        <div class="card-header bg-gradient-to-r from-blue-50 to-indigo-50">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Monthly Sales ({{ date('Y') }})</h3>
                <div class="text-sm text-gray-600">
                    <span class="font-medium">YTD:</span> ₹{{ number_format($stats['total_sales_ytd'], 2) }}
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="monthly-sales-chart" style="height: 300px;"></div>
        </div>
    </div>

    <!-- Recent Sales and Metal Rates -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Sales -->
        <div class="card hover:shadow-lg transition-shadow duration-300">
            <div class="card-header bg-gradient-to-r from-purple-50 to-pink-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Recent Sales</h3>
                    <a href="{{ route('sales.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 transition-colors duration-200 flex items-center">
                        View all
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    @forelse($recent_sales as $sale)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 transform hover:scale-102">
                            <div>
                                <div class="font-medium text-gray-900">{{ $sale->customer->name }}</div>
                                <div class="text-sm text-gray-500">{{ $sale->invoice_number }}</div>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-gray-900">₹{{ number_format($sale->total_amount, 2) }}</div>
                                <div class="text-sm text-gray-500">{{ $sale->sale_date->format('M d, Y') }}</div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <x-icons.sales class="mx-auto h-12 w-12 text-gray-400" />
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No recent sales</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating a new sale.</p>
                            <div class="mt-6">
                                <a href="{{ route('sales.create') }}" class="btn btn-primary flex items-center justify-center mx-auto">
                                    <x-icons.sales class="-ml-1 mr-2 h-5 w-5" />
                                    New Sale
                                </a>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Metal Rates -->
        <div class="card hover:shadow-lg transition-shadow duration-300">
            <div class="card-header bg-gradient-to-r from-yellow-50 to-amber-50">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-medium text-gray-900">Current Metal Rates</h3>
                    <a href="{{ route('metal-rates.index') }}" class="text-sm text-indigo-600 hover:text-indigo-800 transition-colors duration-200 flex items-center">
                        Manage rates
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    @forelse($metal_rates as $rate)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 transform hover:scale-102">
                            <div>
                                <div class="font-medium text-gray-900">{{ $rate->metal_type }} {{ $rate->purity }}</div>
                                <div class="text-sm text-gray-500">Per gram</div>
                            </div>
                            <div class="text-right">
                                <div class="font-medium text-gray-900">₹{{ number_format($rate->rate_per_gram, 2) }}</div>
                                <div class="text-sm text-gray-500">{{ $rate->effective_date->format('M d') }}</div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <x-icons.metal-rates class="mx-auto h-12 w-12 text-gray-400" />
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No metal rates configured</h3>
                            <p class="mt-1 text-sm text-gray-500">Add metal rates to start calculating prices.</p>
                            <div class="mt-6">
                                <a href="{{ route('metal-rates.create') }}" class="btn btn-primary flex items-center justify-center mx-auto">
                                    <x-icons.metal-rates class="-ml-1 mr-2 h-5 w-5" />
                                    Add Rate
                                </a>
                            </div>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
    
    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const monthlySalesData = {!! json_encode($monthly_sales) !!};
            
            // Extract months and totals
            const months = monthlySalesData.map(item => item.month);
            const totals = monthlySalesData.map(item => item.total);
            
            // Create the chart
            const options = {
                chart: {
                    type: 'bar',
                    height: 300,
                    toolbar: {
                        show: false
                    }
                },
                series: [{
                    name: 'Sales',
                    data: totals
                }],
                xaxis: {
                    categories: months
                },
                colors: ['#4F46E5'],
                plotOptions: {
                    bar: {
                        borderRadius: 4,
                        columnWidth: '60%',
                    }
                },
                dataLabels: {
                    enabled: false
                },
                yaxis: {
                    labels: {
                        formatter: function(value) {
                            return '₹' + new Intl.NumberFormat('en-IN').format(value);
                        }
                    }
                },
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return '₹' + new Intl.NumberFormat('en-IN').format(value);
                        }
                    }
                }
            };
            
            const chart = new ApexCharts(document.querySelector("#monthly-sales-chart"), options);
            chart.render();
        });
    </script>
    @endpush
</x-app-layout>
