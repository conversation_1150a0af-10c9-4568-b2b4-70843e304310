<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Convert Estimate to Sale') }} - #{{ $estimate->estimate_number }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Estimate Summary -->
                    <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Estimate Summary</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Customer</dt>
                                <dd class="text-sm text-gray-900">{{ $estimate->customer->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Estimate Date</dt>
                                <dd class="text-sm text-gray-900">{{ $estimate->estimate_date->format('M d, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                                <dd class="text-sm text-gray-900">₹{{ number_format($estimate->total_amount, 2) }}</dd>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('estimates.process-conversion', $estimate) }}">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <!-- Sale Date -->
                            <div>
                                <label for="sale_date" class="block text-sm font-medium text-gray-700">Sale Date</label>
                                <input type="date" name="sale_date" id="sale_date" value="{{ date('Y-m-d') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                @error('sale_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Cash Payment -->
                            <div>
                                <label for="cash_payment" class="block text-sm font-medium text-gray-700">Cash Payment</label>
                                <input type="number" name="cash_payment" id="cash_payment" value="0" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('cash_payment')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Card Payment -->
                            <div>
                                <label for="card_payment" class="block text-sm font-medium text-gray-700">Card Payment</label>
                                <input type="number" name="card_payment" id="card_payment" value="0" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('card_payment')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- UPI Payment -->
                            <div>
                                <label for="upi_payment" class="block text-sm font-medium text-gray-700">UPI Payment</label>
                                <input type="number" name="upi_payment" id="upi_payment" value="{{ $estimate->total_amount }}" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('upi_payment')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Old Gold Adjustment -->
                            <div>
                                <label for="old_gold_adjustment" class="block text-sm font-medium text-gray-700">Old Gold Adjustment</label>
                                <input type="number" name="old_gold_adjustment" id="old_gold_adjustment" value="0" step="0.01" min="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('old_gold_adjustment')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Estimate Items Preview -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Items to be Converted</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metal Type</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weight (g)</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate/g</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Making Charges</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        @foreach($estimate->estimateItems as $item)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <strong>{{ $item->item_name }}</strong>
                                                @if($item->description)
                                                    <br><small class="text-gray-500">{{ $item->description }}</small>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->metal_type }} {{ $item->purity }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($item->net_weight, 3) }}g</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format($item->metal_rate, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format($item->making_charges, 2) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format($item->item_total, 2) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Totals -->
                        <div class="border-t pt-6 mb-6">
                            <div class="flex justify-end">
                                <div class="w-64">
                                    <dl class="space-y-2">
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Subtotal</dt>
                                            <dd class="text-sm text-gray-900">₹{{ number_format($estimate->subtotal, 2) }}</dd>
                                        </div>
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Tax (3% GST)</dt>
                                            <dd class="text-sm text-gray-900">₹{{ number_format($estimate->total_tax, 2) }}</dd>
                                        </div>
                                        @if($estimate->discount_amount > 0)
                                        <div class="flex justify-between">
                                            <dt class="text-sm font-medium text-gray-500">Discount</dt>
                                            <dd class="text-sm text-gray-900">-₹{{ number_format($estimate->discount_amount, 2) }}</dd>
                                        </div>
                                        @endif
                                        <div class="flex justify-between border-t pt-2">
                                            <dt class="text-base font-medium text-gray-900">Total</dt>
                                            <dd class="text-base font-medium text-gray-900">₹{{ number_format($estimate->total_amount, 2) }}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Sale Notes</label>
                            <textarea name="notes" id="notes" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Additional notes for the sale...">{{ $estimate->notes }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('estimates.show', $estimate) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Convert to Sale
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Calculate total payment amount
        function calculateTotalPayment() {
            const cashPayment = parseFloat(document.getElementById('cash_payment').value) || 0;
            const cardPayment = parseFloat(document.getElementById('card_payment').value) || 0;
            const upiPayment = parseFloat(document.getElementById('upi_payment').value) || 0;
            const oldGoldAdjustment = parseFloat(document.getElementById('old_gold_adjustment').value) || 0;

            const totalPayment = cashPayment + cardPayment + upiPayment + oldGoldAdjustment;
            const estimateTotal = {{ $estimate->total_amount }};

            // Update display if you want to show total payment
            // You can add a display element to show the total payment vs estimate amount
        }

        // Add event listeners to payment fields
        ['cash_payment', 'card_payment', 'upi_payment', 'old_gold_adjustment'].forEach(fieldId => {
            document.getElementById(fieldId).addEventListener('input', calculateTotalPayment);
        });

        // Initialize calculation
        calculateTotalPayment();
    </script>
</x-app-layout>
