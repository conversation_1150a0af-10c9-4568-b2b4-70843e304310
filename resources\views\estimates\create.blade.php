<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Estimate') }}
            </h2>
            <a href="{{ route('estimates.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Estimates
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('estimates.store') }}" id="estimateForm">
                        @csrf

                        <!-- Customer and Date Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Estimate Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div>
                                    <label for="customer_id" class="block text-sm font-medium text-gray-700">Customer *</label>
                                    <select name="customer_id" id="customer_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Customer</option>
                                        @foreach($customers as $customer)
                                            <option value="{{ $customer->id }}" {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }} - {{ $customer->mobile }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('customer_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="estimate_date" class="block text-sm font-medium text-gray-700">Estimate Date *</label>
                                    <input type="date" name="estimate_date" id="estimate_date" value="{{ old('estimate_date', today()->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('estimate_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="valid_till" class="block text-sm font-medium text-gray-700">Valid Till *</label>
                                    <input type="date" name="valid_till" id="valid_till" value="{{ old('valid_till', today()->addDays(30)->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('valid_till')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="flex items-center">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="rate_locked" value="1" {{ old('rate_locked') ? 'checked' : '' }}
                                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <span class="ml-2 text-sm text-gray-700">Lock Current Rates</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Estimate Items -->
                        <div class="mb-8">
                            <div class="flex justify-between items-center mb-4">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900">Estimate Items</h3>
                                    @if(request()->has('metal_type'))
                                        <div class="flex items-center mt-1">
                                            <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-sm text-green-600">Pre-filled from calculator</span>
                                        </div>
                                    @endif
                                </div>
                                <button type="button" id="addItem" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Add Item
                                </button>
                            </div>

                            <div id="itemsContainer">
                                <!-- Items will be added here dynamically -->
                            </div>
                        </div>

                        <!-- Billing Summary -->
                        <div class="mb-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Billing Summary</h3>
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Subtotal</label>
                                        <div class="text-lg font-bold text-gray-900" id="subtotalDisplay">₹0.00</div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Tax (3%)</label>
                                        <div class="text-lg font-bold text-gray-900" id="taxDisplay">₹0.00</div>
                                    </div>
                                    <div>
                                        <label for="discount_amount" class="block text-sm font-medium text-gray-700">Discount</label>
                                        <input type="number" name="discount_amount" id="discount_amount" value="{{ old('discount_amount', 0) }}" 
                                               step="0.01" min="0"
                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    </div>
                                </div>
                                <div class="mt-4 pt-4 border-t">
                                    <div class="flex justify-between items-center">
                                        <span class="text-xl font-bold text-gray-900">Total Amount:</span>
                                        <span class="text-xl font-bold text-gray-900" id="totalAmountDisplay">₹0.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-8">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('notes') }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex justify-end space-x-4">
                            <a href="{{ route('estimates.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Estimate
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let itemIndex = 0;
        const metalRates = @json($metalRates);
        const calculatorData = @json(request()->all());

        document.addEventListener('DOMContentLoaded', function() {
            const addItemButton = document.getElementById('addItem');
            const itemsContainer = document.getElementById('itemsContainer');
            const discountInput = document.getElementById('discount_amount');

            // Add first item by default
            addItem();

            // If we have calculator data, pre-fill the first item
            if (calculatorData.metal_type) {
                prefillFromCalculator();
            }

            addItemButton.addEventListener('click', addItem);
            discountInput.addEventListener('input', updateTotals);

            function prefillFromCalculator() {
                if (!calculatorData.metal_type) return;

                // Wait for the first item to be added
                setTimeout(() => {
                    const firstItem = itemsContainer.querySelector('.border');
                    if (!firstItem) return;

                    // Pre-fill item name
                    const itemNameInput = firstItem.querySelector('input[name*="[item_name]"]');
                    if (itemNameInput) {
                        itemNameInput.value = `${calculatorData.metal_type} ${calculatorData.purity} Jewelry`;
                    }

                    // Set metal type
                    const metalTypeSelect = firstItem.querySelector('select[name*="[metal_type]"]');
                    if (metalTypeSelect) {
                        metalTypeSelect.value = calculatorData.metal_type;
                        metalTypeSelect.dispatchEvent(new Event('change'));
                    }

                    // Wait for purity options to load, then set purity
                    setTimeout(() => {
                        const puritySelect = firstItem.querySelector('select[name*="[purity]"]');
                        if (puritySelect) {
                            puritySelect.value = calculatorData.purity;
                            puritySelect.dispatchEvent(new Event('change'));
                        }

                        // Set weights
                        const grossWeightInput = firstItem.querySelector('input[name*="[gross_weight]"]');
                        const stoneWeightInput = firstItem.querySelector('input[name*="[stone_weight]"]');
                        const wastagePercentageInput = firstItem.querySelector('input[name*="[wastage_percentage]"]');
                        const makingChargesInput = firstItem.querySelector('input[name*="[making_charges]"]');
                        const stoneChargesInput = firstItem.querySelector('input[name*="[stone_charges]"]');

                        if (grossWeightInput) grossWeightInput.value = calculatorData.gross_weight || '';
                        if (stoneWeightInput) stoneWeightInput.value = calculatorData.stone_weight || '0';
                        if (wastagePercentageInput) wastagePercentageInput.value = calculatorData.wastage_percentage || '0';
                        if (makingChargesInput) makingChargesInput.value = calculatorData.making_charges || '0';
                        if (stoneChargesInput) stoneChargesInput.value = calculatorData.stone_charges || '0';

                        // Trigger calculations
                        if (grossWeightInput) grossWeightInput.dispatchEvent(new Event('input'));
                    }, 100);
                }, 100);
            }

            function addItem() {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'border border-gray-200 rounded-lg p-4 mb-4';
                itemDiv.innerHTML = `
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-medium text-gray-900">Item ${itemIndex + 1}</h4>
                        <button type="button" class="remove-item bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm">
                            Remove
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Item Name *</label>
                            <input type="text" name="items[${itemIndex}][item_name]" required
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Metal Type *</label>
                            <select name="items[${itemIndex}][metal_type]" class="metal-type mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                <option value="">Select Metal</option>
                                <option value="Gold">Gold</option>
                                <option value="Silver">Silver</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Purity *</label>
                            <select name="items[${itemIndex}][purity]" class="purity mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                                <option value="">Select Purity</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Gross Weight (g) *</label>
                            <input type="number" name="items[${itemIndex}][gross_weight]" step="0.001" min="0" required
                                   class="gross-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Stone Weight (g)</label>
                            <input type="number" name="items[${itemIndex}][stone_weight]" step="0.001" min="0" value="0"
                                   class="stone-weight mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Net Weight (g)</label>
                            <input type="number" name="items[${itemIndex}][net_weight]" step="0.001" readonly
                                   class="net-weight mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Wastage %</label>
                            <input type="number" name="items[${itemIndex}][wastage_percentage]" step="0.01" min="0" max="100" value="0"
                                   class="wastage-percentage mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Metal Rate (₹/g) *</label>
                            <input type="number" name="items[${itemIndex}][metal_rate]" step="0.01" min="0" required
                                   class="metal-rate mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <p class="text-xs text-gray-500 current-rate-display"></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Making Charges (₹) *</label>
                            <input type="number" name="items[${itemIndex}][making_charges]" step="0.01" min="0" required
                                   class="making-charges mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Stone Charges (₹)</label>
                            <input type="number" name="items[${itemIndex}][stone_charges]" step="0.01" min="0" value="0"
                                   class="stone-charges mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Wastage Amount (₹)</label>
                            <input type="number" name="items[${itemIndex}][wastage_amount]" step="0.01" readonly
                                   class="wastage-amount mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm">
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea name="items[${itemIndex}][description]" rows="2"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"></textarea>
                        </div>
                        <div class="flex items-end">
                            <div class="w-full">
                                <label class="block text-sm font-medium text-gray-700">Item Total (₹)</label>
                                <input type="number" name="items[${itemIndex}][item_total]" step="0.01" readonly
                                       class="item-total mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm text-lg font-bold">
                            </div>
                        </div>
                    </div>
                `;

                itemsContainer.appendChild(itemDiv);

                // Add event listeners for this item
                setupItemEventListeners(itemDiv, itemIndex);

                itemIndex++;
            }

            function setupItemEventListeners(itemDiv, index) {
                const metalTypeSelect = itemDiv.querySelector('.metal-type');
                const puritySelect = itemDiv.querySelector('.purity');
                const grossWeightInput = itemDiv.querySelector('.gross-weight');
                const stoneWeightInput = itemDiv.querySelector('.stone-weight');
                const netWeightInput = itemDiv.querySelector('.net-weight');
                const wastagePercentageInput = itemDiv.querySelector('.wastage-percentage');
                const wastageAmountInput = itemDiv.querySelector('.wastage-amount');
                const metalRateInput = itemDiv.querySelector('.metal-rate');
                const makingChargesInput = itemDiv.querySelector('.making-charges');
                const stoneChargesInput = itemDiv.querySelector('.stone-charges');
                const itemTotalInput = itemDiv.querySelector('.item-total');
                const currentRateDisplay = itemDiv.querySelector('.current-rate-display');
                const removeButton = itemDiv.querySelector('.remove-item');

                // Purity options
                const purityOptions = {
                    'Gold': ['22K', '18K', '14K'],
                    'Silver': ['925', '999']
                };

                metalTypeSelect.addEventListener('change', function() {
                    const selectedMetal = this.value;
                    puritySelect.innerHTML = '<option value="">Select Purity</option>';
                    
                    if (selectedMetal && purityOptions[selectedMetal]) {
                        purityOptions[selectedMetal].forEach(purity => {
                            const option = document.createElement('option');
                            option.value = purity;
                            option.textContent = purity;
                            puritySelect.appendChild(option);
                        });
                    }
                    updateCurrentRate();
                });

                puritySelect.addEventListener('change', updateCurrentRate);

                [grossWeightInput, stoneWeightInput, wastagePercentageInput, metalRateInput, makingChargesInput, stoneChargesInput].forEach(input => {
                    input.addEventListener('input', calculateItemTotal);
                });

                removeButton.addEventListener('click', function() {
                    if (itemsContainer.children.length > 1) {
                        itemDiv.remove();
                        updateTotals();
                    } else {
                        alert('At least one item is required');
                    }
                });

                function updateCurrentRate() {
                    const metalType = metalTypeSelect.value;
                    const purity = puritySelect.value;
                    
                    if (metalType && purity) {
                        const rateKey = `${metalType}.${purity}`;
                        const rate = metalRates[rateKey];
                        
                        if (rate) {
                            currentRateDisplay.textContent = `Current rate: ₹${rate.rate_per_gram}/gram`;
                            metalRateInput.value = rate.rate_per_gram;
                            calculateItemTotal();
                        } else {
                            currentRateDisplay.textContent = 'No current rate available';
                        }
                    } else {
                        currentRateDisplay.textContent = '';
                    }
                }

                function calculateItemTotal() {
                    const grossWeight = parseFloat(grossWeightInput.value) || 0;
                    const stoneWeight = parseFloat(stoneWeightInput.value) || 0;
                    const netWeight = grossWeight - stoneWeight;
                    
                    const wastagePercentage = parseFloat(wastagePercentageInput.value) || 0;
                    const wastageAmount = (netWeight * wastagePercentage / 100) * (parseFloat(metalRateInput.value) || 0);
                    
                    const metalRate = parseFloat(metalRateInput.value) || 0;
                    const makingCharges = parseFloat(makingChargesInput.value) || 0;
                    const stoneCharges = parseFloat(stoneChargesInput.value) || 0;
                    
                    const metalValue = netWeight * metalRate;
                    const itemTotal = metalValue + makingCharges + stoneCharges + wastageAmount;
                    
                    netWeightInput.value = netWeight.toFixed(3);
                    wastageAmountInput.value = wastageAmount.toFixed(2);
                    itemTotalInput.value = itemTotal.toFixed(2);
                    
                    updateTotals();
                }
            }

            function updateTotals() {
                let subtotal = 0;
                
                document.querySelectorAll('.item-total').forEach(input => {
                    subtotal += parseFloat(input.value) || 0;
                });
                
                const tax = subtotal * 0.03;
                const discount = parseFloat(discountInput.value) || 0;
                const total = subtotal + tax - discount;
                
                document.getElementById('subtotalDisplay').textContent = `₹${subtotal.toFixed(2)}`;
                document.getElementById('taxDisplay').textContent = `₹${tax.toFixed(2)}`;
                document.getElementById('totalAmountDisplay').textContent = `₹${total.toFixed(2)}`;
            }
        });
    </script>
</x-app-layout>
