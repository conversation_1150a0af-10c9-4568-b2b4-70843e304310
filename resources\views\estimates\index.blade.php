<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Estimates') }}
            </h2>
            @can('create_estimate')
                <a href="{{ route('estimates.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Create New Estimate
                </a>
            @endcan
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('estimates.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search by estimate number, customer..." 
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Approved</option>
                                <option value="converted" {{ request('status') === 'converted' ? 'selected' : '' }}>Converted</option>
                                <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                            </select>
                        </div>
                        <div>
                            <select name="validity" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Validity</option>
                                <option value="valid" {{ request('validity') === 'valid' ? 'selected' : '' }}>Valid</option>
                                <option value="expired" {{ request('validity') === 'expired' ? 'selected' : '' }}>Expired</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" name="from_date" value="{{ request('from_date') }}" 
                                   placeholder="From Date"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="{{ route('estimates.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Estimates Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estimate #</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Validity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($estimates as $estimate)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $estimate->estimate_number }}</div>
                                            <div class="text-sm text-gray-500">{{ $estimate->estimate_date->format('d M, Y') }}</div>
                                            @if($estimate->rate_locked)
                                                <div class="text-xs text-blue-600">Rate Locked</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $estimate->customer->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $estimate->customer->mobile }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $estimate->estimateItems->count() }} item(s)</div>
                                            <div class="text-sm text-gray-500">
                                                @foreach($estimate->estimateItems->take(2) as $item)
                                                    {{ $item->item_name }}@if(!$loop->last), @endif
                                                @endforeach
                                                @if($estimate->estimateItems->count() > 2)
                                                    ...
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">₹{{ number_format($estimate->total_amount, 2) }}</div>
                                            @if($estimate->discount_amount > 0)
                                                <div class="text-sm text-green-600">Discount: ₹{{ number_format($estimate->discount_amount, 2) }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $estimate->valid_till->format('d M, Y') }}</div>
                                            @if($estimate->is_expired)
                                                <div class="text-sm text-red-600">Expired</div>
                                            @else
                                                <div class="text-sm text-green-600">Valid</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $estimate->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                                                   ($estimate->status === 'approved' ? 'bg-green-100 text-green-800' : 
                                                   ($estimate->status === 'converted' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800')) }}">
                                                {{ ucfirst($estimate->status) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                @can('view_estimates')
                                                    <a href="{{ route('estimates.show', $estimate) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                @endcan
                                                @can('edit_estimate')
                                                    @if($estimate->status !== 'converted')
                                                        <a href="{{ route('estimates.edit', $estimate) }}" class="text-blue-600 hover:text-blue-900">Edit</a>
                                                    @endif
                                                @endcan
                                                @can('convert_estimate')
                                                    @if($estimate->can_convert)
                                                        <a href="{{ route('estimates.convert', $estimate) }}" class="text-green-600 hover:text-green-900">Convert</a>
                                                    @endif
                                                @endcan
                                                <a href="{{ route('estimates.print', $estimate) }}" target="_blank" class="text-purple-600 hover:text-purple-900">Print</a>
                                                @can('delete_estimate')
                                                    @if($estimate->status !== 'converted')
                                                        <form method="POST" action="{{ route('estimates.destroy', $estimate) }}" class="inline" 
                                                              onsubmit="return confirm('Are you sure you want to delete this estimate?')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                                        </form>
                                                    @endif
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            No estimates found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $estimates->links() }}
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            @if($estimates->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Estimates</div>
                            <div class="text-2xl font-bold text-gray-900">{{ $estimates->total() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Pending Estimates</div>
                            <div class="text-2xl font-bold text-yellow-600">{{ $estimates->where('status', 'pending')->count() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Converted Estimates</div>
                            <div class="text-2xl font-bold text-green-600">{{ $estimates->where('status', 'converted')->count() }}</div>
                        </div>
                    </div>
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="text-sm font-medium text-gray-500">Total Value</div>
                            <div class="text-2xl font-bold text-gray-900">₹{{ number_format($estimates->sum('total_amount'), 2) }}</div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
