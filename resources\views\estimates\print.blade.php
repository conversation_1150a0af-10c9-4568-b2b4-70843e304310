<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estimate - {{ $estimate->estimate_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.2;
            color: #000;
            margin: 0;
            padding: 5px;
            width: 80mm;
            max-width: 80mm;
        }
        .center { text-align: center; }
        .left { text-align: left; }
        .right { text-align: right; }
        .bold { font-weight: bold; }
        .small { font-size: 9px; }
        .dotted-line {
            border-bottom: 1px dotted #000;
            margin: 3px 0;
        }
        .header {
            text-align: center;
            margin-bottom: 8px;
        }
        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        .estimate-title {
            font-size: 12px;
            font-weight: bold;
            margin: 5px 0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 1px 0;
        }
        .item-row {
            margin: 2px 0;
            padding: 1px 0;
        }
        .amount-right {
            text-align: right;
            float: right;
        }
        .total-section {
            border-top: 1px solid #000;
            margin-top: 5px;
            padding-top: 3px;
        }
        .grand-total {
            font-weight: bold;
            font-size: 12px;
            border-top: 1px solid #000;
            border-bottom: 1px solid #000;
            padding: 2px 0;
            margin: 3px 0;
        }
        @media print {
            body {
                padding: 0;
                margin: 0;
            }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">JEWEL PRO</div>
        <div class="small">Premium Jewelry Store</div>
        <div class="small">Ph: +91 98765 43210</div>
        <div class="small">GSTIN: 29ABCDE1234F1Z5</div>
    </div>

    <div class="dotted-line"></div>

    <div class="estimate-title center">SALES ESTIMATE</div>

    <div class="dotted-line"></div>

    <!-- Estimate Info -->
    <div class="info-row">
        <span>Est No: {{ $estimate->estimate_number }}</span>
        <span>{{ $estimate->estimate_date->format('d-m-Y') }}</span>
    </div>

    <div class="info-row">
        <span>Silver Rate: ₹{{ number_format(75.45, 2) }}</span>
        <span class="small">{{ $estimate->estimate_date->format('H:i') }}</span>
    </div>

    <div class="dotted-line"></div>

    <!-- Customer Details -->
    <div>
        <strong>{{ $estimate->customer->name }}</strong><br>
        <span class="small">{{ $estimate->customer->mobile }}</span>
    </div>

    <div class="dotted-line"></div>

    <!-- Items -->
    @foreach($estimate->estimateItems as $index => $item)
        <div class="item-row">
            <div class="bold">{{ $item->item_name }} {{ $item->metal_type }} {{ $item->purity }}</div>
            <div class="info-row">
                <span>Gs.Wt: {{ number_format($item->gross_weight, 3) }}</span>
                <span>Amount: ₹{{ number_format($item->item_total, 2) }}</span>
            </div>
            <div class="info-row">
                <span>GOLD COIN: {{ number_format($item->net_weight, 3) }}</span>
                <span>{{ number_format($item->metal_rate, 2) }}</span>
            </div>
            @if($item->making_charges > 0)
            <div class="info-row">
                <span>Making: ₹{{ number_format($item->making_charges, 2) }}</span>
                <span></span>
            </div>
            @endif
            @if($item->stone_charges > 0)
            <div class="info-row">
                <span>Stone: ₹{{ number_format($item->stone_charges, 2) }}</span>
                <span></span>
            </div>
            @endif
            @if($item->huid_number)
            <div class="small">HUID: {{ $item->huid_number }}</div>
            @elseif($item->huid_required)
            <div class="small">HUID Required</div>
            @endif
        </div>
        <div class="dotted-line"></div>
    @endforeach

    <!-- Totals Section -->
    <div class="total-section">
        <div class="info-row">
            <span>Subtotal</span>
            <span>₹{{ number_format($estimate->subtotal, 2) }}</span>
        </div>

        <div class="info-row">
            <span>SGST Amt (1.5%)</span>
            <span>₹{{ number_format($estimate->total_tax / 2, 2) }}</span>
        </div>

        <div class="info-row">
            <span>CGST Amt (1.5%)</span>
            <span>₹{{ number_format($estimate->total_tax / 2, 2) }}</span>
        </div>

        @if($estimate->discount_amount > 0)
        <div class="info-row">
            <span>Discount</span>
            <span>-₹{{ number_format($estimate->discount_amount, 2) }}</span>
        </div>
        @endif

        <div class="info-row grand-total">
            <span>FINAL AMOUNT</span>
            <span>₹{{ number_format($estimate->total_amount, 2) }}</span>
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="terms">
        <div class="section-title">Terms & Conditions:</div>
        1. This is an estimate only and not a final invoice.<br>
        2. Prices are subject to change based on current metal rates (unless locked).<br>
        3. HUID certification will be provided for applicable items before final billing.<br>
        4. Estimate validity is limited to the date mentioned above.<br>
        5. Final billing may vary based on actual weight and current rates.<br>
        6. Advance payment may be required to proceed with the order.
    </div>

    <!-- Footer -->
    <div class="footer">
        <strong>Thank you for choosing Jewel Pro!</strong><br>
        This is a computer generated estimate. For any queries, please contact <NAME_EMAIL>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
