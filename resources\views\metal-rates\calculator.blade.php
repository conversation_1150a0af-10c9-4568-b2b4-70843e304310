<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Jewelry Price Calculator') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('metal-rates.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Metal Rates
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Current Rates Display -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Metal Rates</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @forelse($currentRates as $metalType => $purities)
                            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
                                <h4 class="text-lg font-semibold text-gray-900 mb-3">{{ $metalType }}</h4>
                                @foreach($purities as $purity => $rate)
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-700">{{ $purity }}</span>
                                        <div class="text-right">
                                            <div class="text-lg font-bold text-gray-900">₹{{ number_format($rate->rate_per_gram, 2) }}/g</div>
                                            <div class="text-xs text-gray-500">₹{{ number_format($rate->rate_per_10_gram, 2) }}/10g</div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @empty
                            <div class="col-span-3 text-center text-gray-500 py-8">
                                No active rates found. Please add some metal rates first.
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Calculator Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Price Calculator</h3>
                        
                        <form id="calculatorForm" class="space-y-6">
                            <!-- Metal Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type *</label>
                                    <select id="metal_type" name="metal_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Metal</option>
                                        <option value="Gold">Gold</option>
                                        <option value="Silver">Silver</option>
                                        <option value="Platinum">Platinum</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="purity" class="block text-sm font-medium text-gray-700">Purity *</label>
                                    <select id="purity" name="purity" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Purity</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Weight Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="gross_weight" class="block text-sm font-medium text-gray-700">Gross Weight (grams) *</label>
                                    <div class="relative mt-1">
                                        <input type="number" id="gross_weight" name="gross_weight" step="0.001" min="0" required
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pr-12">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <span class="text-gray-500 text-sm">g</span>
                                        </div>
                                    </div>
                                    <div id="gross_weight_error" class="text-red-500 text-xs mt-1 hidden">Please enter a valid weight</div>
                                </div>
                                <div>
                                    <label for="stone_weight" class="block text-sm font-medium text-gray-700">Stone Weight (grams)</label>
                                    <div class="relative mt-1">
                                        <input type="number" id="stone_weight" name="stone_weight" step="0.001" min="0" value="0"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pr-12">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <span class="text-gray-500 text-sm">g</span>
                                        </div>
                                    </div>
                                    <div id="stone_weight_error" class="text-red-500 text-xs mt-1 hidden">Stone weight cannot exceed gross weight</div>
                                </div>
                            </div>

                            <!-- Rate and Wastage -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="custom_rate" class="block text-sm font-medium text-gray-700">Custom Rate (₹/gram)</label>
                                    <div class="relative mt-1">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                                            <span class="text-gray-500 text-sm">₹</span>
                                        </div>
                                        <input type="number" id="custom_rate" name="custom_rate" step="0.01" min="0"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pl-8 pr-12">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <span class="text-gray-500 text-sm">/g</span>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500" id="current_rate_display">Leave blank to use current rate</p>
                                </div>
                                <div>
                                    <label for="wastage_percentage" class="block text-sm font-medium text-gray-700">Wastage (%)</label>
                                    <div class="relative mt-1">
                                        <input type="number" id="wastage_percentage" name="wastage_percentage" step="0.01" min="0" max="100" value="0"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pr-8">
                                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                            <span class="text-gray-500 text-sm">%</span>
                                        </div>
                                    </div>
                                    <div id="wastage_error" class="text-red-500 text-xs mt-1 hidden">Wastage must be between 0-100%</div>
                                </div>
                            </div>

                            <!-- Charges -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="making_charges" class="block text-sm font-medium text-gray-700">Making Charges (₹)</label>
                                    <div class="relative mt-1">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                                            <span class="text-gray-500 text-sm">₹</span>
                                        </div>
                                        <input type="number" id="making_charges" name="making_charges" step="0.01" min="0" value="0"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pl-8">
                                    </div>
                                </div>
                                <div>
                                    <label for="stone_charges" class="block text-sm font-medium text-gray-700">Stone Charges (₹)</label>
                                    <div class="relative mt-1">
                                        <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                                            <span class="text-gray-500 text-sm">₹</span>
                                        </div>
                                        <input type="number" id="stone_charges" name="stone_charges" step="0.01" min="0" value="0"
                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 pl-8">
                                    </div>
                                </div>
                            </div>

                            <!-- Live Calculation Notice -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    <p class="text-sm text-green-700 font-medium">Live Calculation Enabled</p>
                                </div>
                                <p class="text-xs text-green-600 mt-1">Results update automatically as you type</p>
                            </div>

                            <!-- Calculate Button (for manual trigger) -->
                            <div>
                                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg text-lg">
                                    Recalculate
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Results Display -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-6">Calculation Results</h3>
                        
                        <div id="calculationResults" class="hidden">
                            <!-- Weight Breakdown -->
                            <div class="mb-6">
                                <h4 class="text-md font-semibold text-gray-800 mb-3">Weight Breakdown</h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Gross Weight:</span>
                                        <span id="result_gross_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Stone Weight:</span>
                                        <span id="result_stone_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2">
                                        <span class="text-gray-600">Net Weight:</span>
                                        <span id="result_net_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Wastage Weight:</span>
                                        <span id="result_wastage_weight" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2 font-semibold">
                                        <span class="text-gray-800">Total Weight:</span>
                                        <span id="result_total_weight" class="text-gray-800">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Price Breakdown -->
                            <div class="mb-6">
                                <h4 class="text-md font-semibold text-gray-800 mb-3">Price Breakdown</h4>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Rate per Gram:</span>
                                        <span id="result_rate_per_gram" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Metal Value:</span>
                                        <span id="result_metal_value" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Making Charges:</span>
                                        <span id="result_making_charges" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Stone Charges:</span>
                                        <span id="result_stone_charges" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2">
                                        <span class="text-gray-600">Subtotal:</span>
                                        <span id="result_subtotal" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">GST (3%):</span>
                                        <span id="result_gst_amount" class="font-medium">-</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-2 text-lg font-bold">
                                        <span class="text-gray-800">Total Amount:</span>
                                        <span id="result_total_amount" class="text-green-600">-</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                                <button id="createEstimate" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Create Estimate
                                </button>
                                <button id="saveCalculation" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Save Calculation
                                </button>
                                <button id="addToSale" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                    Add to Sale
                                </button>
                            </div>
                        </div>

                        <div id="noResults" class="text-center text-gray-500 py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                            <p class="mt-2">Enter details above and click Calculate to see results</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Calculation History -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mt-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Recent Calculations</h3>
                        <button type="button" id="clearHistory" class="text-red-600 hover:text-red-800 text-sm">
                            Clear History
                        </button>
                    </div>
                    <div id="calculationHistory" class="space-y-2">
                        <p class="text-gray-500 text-sm">No saved calculations yet</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const currentRates = @json($currentRates);
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const customRateInput = document.getElementById('custom_rate');
            const currentRateDisplay = document.getElementById('current_rate_display');
            const calculatorForm = document.getElementById('calculatorForm');
            const calculationResults = document.getElementById('calculationResults');
            const noResults = document.getElementById('noResults');

            // Get all input elements for live calculation
            const grossWeightInput = document.getElementById('gross_weight');
            const stoneWeightInput = document.getElementById('stone_weight');
            const wastagePercentageInput = document.getElementById('wastage_percentage');
            const makingChargesInput = document.getElementById('making_charges');
            const stoneChargesInput = document.getElementById('stone_charges');

            // Purity options based on metal type
            const purityOptions = {
                'Gold': ['22K', '18K', '14K'],
                'Silver': ['925', '999'],
                'Platinum': ['950', '900']
            };

            // Update purity options when metal type changes
            metalTypeSelect.addEventListener('change', function() {
                const selectedMetal = this.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';

                if (selectedMetal && purityOptions[selectedMetal]) {
                    purityOptions[selectedMetal].forEach(purity => {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        puritySelect.appendChild(option);
                    });
                }

                updateCurrentRateDisplay();
                performLiveCalculation();
            });

            // Update current rate display when purity changes
            puritySelect.addEventListener('change', function() {
                updateCurrentRateDisplay();
                performLiveCalculation();
            });

            // Add live calculation listeners to all input fields
            [grossWeightInput, stoneWeightInput, wastagePercentageInput, makingChargesInput, stoneChargesInput, customRateInput].forEach(input => {
                if (input) {
                    input.addEventListener('input', performLiveCalculation);
                    input.addEventListener('change', performLiveCalculation);
                }
            });

            // Load calculation history on page load
            loadCalculationHistory();

            function updateCurrentRateDisplay() {
                const metalType = metalTypeSelect.value;
                const purity = puritySelect.value;

                if (metalType && purity && currentRates[metalType] && currentRates[metalType][purity]) {
                    const rate = currentRates[metalType][purity];
                    currentRateDisplay.textContent = `Current rate: ₹${rate.rate_per_gram}/gram`;
                    if (!customRateInput.value) {
                        customRateInput.placeholder = rate.rate_per_gram;
                    }
                } else {
                    currentRateDisplay.textContent = 'Leave blank to use current rate';
                    customRateInput.placeholder = '';
                }
            }

            // Input validation function
            function validateInputs() {
                let isValid = true;

                // Clear previous errors
                document.querySelectorAll('.text-red-500').forEach(el => el.classList.add('hidden'));
                document.querySelectorAll('input').forEach(input => {
                    input.classList.remove('border-red-300', 'focus:border-red-300', 'focus:ring-red-200');
                });

                const grossWeight = parseFloat(grossWeightInput.value) || 0;
                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;
                const wastagePercentage = parseFloat(wastagePercentageInput.value) || 0;

                // Validate gross weight
                if (grossWeight <= 0) {
                    showError('gross_weight', 'Please enter a valid weight');
                    isValid = false;
                }

                // Validate stone weight
                if (stoneWeight > grossWeight) {
                    showError('stone_weight', 'Stone weight cannot exceed gross weight');
                    isValid = false;
                }

                // Validate wastage percentage
                if (wastagePercentage < 0 || wastagePercentage > 100) {
                    showError('wastage_percentage', 'Wastage must be between 0-100%');
                    isValid = false;
                }

                return isValid;
            }

            function showError(fieldName, message) {
                const errorElement = document.getElementById(fieldName + '_error');
                const inputElement = document.getElementById(fieldName);

                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.classList.remove('hidden');
                }

                if (inputElement) {
                    inputElement.classList.add('border-red-300', 'focus:border-red-300', 'focus:ring-red-200');
                }
            }

            // Live calculation function
            function performLiveCalculation() {
                const metalType = metalTypeSelect.value;
                const purity = puritySelect.value;
                const grossWeight = parseFloat(grossWeightInput.value) || 0;
                const stoneWeight = parseFloat(stoneWeightInput.value) || 0;
                const wastagePercentage = parseFloat(wastagePercentageInput.value) || 0;
                const makingCharges = parseFloat(makingChargesInput.value) || 0;
                const stoneCharges = parseFloat(stoneChargesInput.value) || 0;
                const customRate = parseFloat(customRateInput.value) || 0;

                // Validate inputs
                if (!validateInputs()) {
                    hideResults();
                    return;
                }

                // Check if we have minimum required data
                if (!metalType || !purity || grossWeight <= 0) {
                    hideResults();
                    return;
                }

                // Get rate
                let rate = customRate;
                if (!rate && currentRates[metalType] && currentRates[metalType][purity]) {
                    rate = parseFloat(currentRates[metalType][purity].rate_per_gram);
                }

                if (!rate) {
                    hideResults();
                    return;
                }

                // Perform calculations
                const netWeight = Math.max(0, grossWeight - stoneWeight);
                const wastageWeight = (netWeight * wastagePercentage) / 100;
                const totalWeight = netWeight + wastageWeight;
                const metalValue = totalWeight * rate;
                const subtotal = metalValue + makingCharges + stoneCharges;
                const gstAmount = subtotal * 0.03; // 3% GST
                const totalAmount = subtotal + gstAmount;

                // Display results
                displayCalculationResults({
                    gross_weight: grossWeight,
                    stone_weight: stoneWeight,
                    net_weight: netWeight,
                    wastage_percentage: wastagePercentage,
                    wastage_weight: wastageWeight,
                    total_weight: totalWeight,
                    rate_per_gram: rate,
                    metal_value: metalValue,
                    making_charges: makingCharges,
                    stone_charges: stoneCharges,
                    subtotal: subtotal,
                    gst_amount: gstAmount,
                    total_amount: totalAmount
                });
            }

            function hideResults() {
                calculationResults.classList.add('hidden');
                noResults.classList.remove('hidden');
            }

            function displayCalculationResults(calc) {
                // Weight breakdown
                document.getElementById('result_gross_weight').textContent = `${calc.gross_weight.toFixed(3)}g`;
                document.getElementById('result_stone_weight').textContent = `${calc.stone_weight.toFixed(3)}g`;
                document.getElementById('result_net_weight').textContent = `${calc.net_weight.toFixed(3)}g`;
                document.getElementById('result_wastage_weight').textContent = `${calc.wastage_weight.toFixed(3)}g (${calc.wastage_percentage}%)`;
                document.getElementById('result_total_weight').textContent = `${calc.total_weight.toFixed(3)}g`;

                // Price breakdown
                document.getElementById('result_rate_per_gram').textContent = `₹${calc.rate_per_gram.toFixed(2)}`;
                document.getElementById('result_metal_value').textContent = `₹${calc.metal_value.toFixed(2)}`;
                document.getElementById('result_making_charges').textContent = `₹${calc.making_charges.toFixed(2)}`;
                document.getElementById('result_stone_charges').textContent = `₹${calc.stone_charges.toFixed(2)}`;
                document.getElementById('result_subtotal').textContent = `₹${calc.subtotal.toFixed(2)}`;
                document.getElementById('result_gst_amount').textContent = `₹${calc.gst_amount.toFixed(2)}`;
                document.getElementById('result_total_amount').textContent = `₹${calc.total_amount.toFixed(2)}`;

                // Show results
                noResults.classList.add('hidden');
                calculationResults.classList.remove('hidden');
            }

            // Handle form submission (now just triggers live calculation)
            calculatorForm.addEventListener('submit', function(e) {
                e.preventDefault();
                performLiveCalculation();
            });

            function displayResults(calc) {
                displayCalculationResults(calc);
            }

            // Quick action buttons
            document.getElementById('createEstimate').addEventListener('click', function() {
                // Check if we have valid calculation results
                if (calculationResults.classList.contains('hidden')) {
                    alert('Please complete the calculation first');
                    return;
                }

                // Gather all calculation data
                const calculationData = {
                    metal_type: metalTypeSelect.value,
                    purity: puritySelect.value,
                    gross_weight: grossWeightInput.value,
                    stone_weight: stoneWeightInput.value,
                    wastage_percentage: wastagePercentageInput.value,
                    making_charges: makingChargesInput.value,
                    stone_charges: stoneChargesInput.value,
                    custom_rate: customRateInput.value,
                    // Add calculated values
                    net_weight: document.getElementById('result_net_weight').textContent.replace('g', ''),
                    total_weight: document.getElementById('result_total_weight').textContent.replace('g', ''),
                    rate_per_gram: document.getElementById('result_rate_per_gram').textContent.replace('₹', ''),
                    metal_value: document.getElementById('result_metal_value').textContent.replace('₹', '').replace(/,/g, ''),
                    subtotal: document.getElementById('result_subtotal').textContent.replace('₹', '').replace(/,/g, ''),
                    gst_amount: document.getElementById('result_gst_amount').textContent.replace('₹', '').replace(/,/g, ''),
                    total_amount: document.getElementById('result_total_amount').textContent.replace('₹', '').replace(/,/g, '')
                };

                // Create URL with calculation data
                const params = new URLSearchParams();
                Object.entries(calculationData).forEach(([key, value]) => {
                    if (value && value !== '0' && value !== '') {
                        params.append(key, value);
                    }
                });

                window.location.href = `/estimates/create?${params.toString()}`;
            });

            document.getElementById('addToSale').addEventListener('click', function() {
                // Redirect to sales creation with pre-filled data
                const params = new URLSearchParams();
                const formData = new FormData(calculatorForm);
                for (let [key, value] of formData.entries()) {
                    if (value) params.append(key, value);
                }
                window.location.href = `/sales/create?${params.toString()}`;
            });

            // Save calculation functionality
            document.getElementById('saveCalculation').addEventListener('click', function() {
                if (calculationResults.classList.contains('hidden')) {
                    alert('Please complete the calculation first');
                    return;
                }

                const calculationData = {
                    metal_type: metalTypeSelect.value,
                    purity: puritySelect.value,
                    gross_weight: grossWeightInput.value,
                    stone_weight: stoneWeightInput.value,
                    wastage_percentage: wastagePercentageInput.value,
                    making_charges: makingChargesInput.value,
                    stone_charges: stoneChargesInput.value,
                    custom_rate: customRateInput.value,
                    total_amount: document.getElementById('result_total_amount').textContent.replace('₹', '').replace(/,/g, ''),
                    timestamp: new Date().toISOString()
                };

                // Save to localStorage
                let savedCalculations = JSON.parse(localStorage.getItem('savedCalculations') || '[]');
                savedCalculations.unshift(calculationData);

                // Keep only last 10 calculations
                if (savedCalculations.length > 10) {
                    savedCalculations = savedCalculations.slice(0, 10);
                }

                localStorage.setItem('savedCalculations', JSON.stringify(savedCalculations));

                // Refresh history display
                loadCalculationHistory();

                // Show success message
                const button = this;
                const originalText = button.textContent;
                button.textContent = 'Saved!';
                button.classList.remove('bg-blue-500', 'hover:bg-blue-700');
                button.classList.add('bg-green-500', 'hover:bg-green-700');

                setTimeout(() => {
                    button.textContent = originalText;
                    button.classList.remove('bg-green-500', 'hover:bg-green-700');
                    button.classList.add('bg-blue-500', 'hover:bg-blue-700');
                }, 2000);
            });

            // Clear history functionality
            document.getElementById('clearHistory').addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all saved calculations?')) {
                    localStorage.removeItem('savedCalculations');
                    loadCalculationHistory();
                }
            });

            function loadCalculationHistory() {
                const historyContainer = document.getElementById('calculationHistory');
                const savedCalculations = JSON.parse(localStorage.getItem('savedCalculations') || '[]');

                if (savedCalculations.length === 0) {
                    historyContainer.innerHTML = '<p class="text-gray-500 text-sm">No saved calculations yet</p>';
                    return;
                }

                historyContainer.innerHTML = savedCalculations.map((calc, index) => `
                    <div class="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 cursor-pointer" onclick="loadCalculation(${index})">
                        <div class="flex justify-between items-start">
                            <div>
                                <div class="font-medium text-sm">${calc.metal_type} ${calc.purity} - ${calc.gross_weight}g</div>
                                <div class="text-xs text-gray-500">${new Date(calc.timestamp).toLocaleString()}</div>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-green-600">₹${parseFloat(calc.total_amount).toLocaleString()}</div>
                                <div class="text-xs text-gray-500">Total</div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            window.loadCalculation = function(index) {
                const savedCalculations = JSON.parse(localStorage.getItem('savedCalculations') || '[]');
                const calc = savedCalculations[index];

                if (!calc) return;

                // Load the calculation data into the form
                metalTypeSelect.value = calc.metal_type;
                metalTypeSelect.dispatchEvent(new Event('change'));

                setTimeout(() => {
                    puritySelect.value = calc.purity;
                    puritySelect.dispatchEvent(new Event('change'));

                    grossWeightInput.value = calc.gross_weight;
                    stoneWeightInput.value = calc.stone_weight;
                    wastagePercentageInput.value = calc.wastage_percentage;
                    makingChargesInput.value = calc.making_charges;
                    stoneChargesInput.value = calc.stone_charges;
                    customRateInput.value = calc.custom_rate;

                    // Trigger calculation
                    performLiveCalculation();
                }, 100);
            };
        });
    </script>
</x-app-layout>
