<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Metal Rate') }}
            </h2>
            <a href="{{ route('metal-rates.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Metal Rates
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-2xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('metal-rates.update', $metalRate) }}">
                        @csrf
                        @method('PUT')

                        <!-- Metal Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type *</label>
                                    <select name="metal_type" id="metal_type" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Metal Type</option>
                                        <option value="Gold" {{ old('metal_type', $metalRate->metal_type) == 'Gold' ? 'selected' : '' }}>Gold</option>
                                        <option value="Silver" {{ old('metal_type', $metalRate->metal_type) == 'Silver' ? 'selected' : '' }}>Silver</option>
                                        <option value="Platinum" {{ old('metal_type', $metalRate->metal_type) == 'Platinum' ? 'selected' : '' }}>Platinum</option>
                                    </select>
                                    @error('metal_type')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="purity" class="block text-sm font-medium text-gray-700">Purity *</label>
                                    <select name="purity" id="purity" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        <option value="">Select Purity</option>
                                        <!-- Options will be populated by JavaScript based on metal type -->
                                    </select>
                                    @error('purity')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Rate Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Rate Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="rate_per_gram" class="block text-sm font-medium text-gray-700">Rate per Gram (₹) *</label>
                                    <input type="number" name="rate_per_gram" id="rate_per_gram" value="{{ old('rate_per_gram', $metalRate->rate_per_gram) }}" 
                                           step="0.01" min="0" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('rate_per_gram')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="rate_per_10_gram" class="block text-sm font-medium text-gray-700">Rate per 10 Grams (₹)</label>
                                    <input type="number" name="rate_per_10_gram" id="rate_per_10_gram" value="{{ old('rate_per_10_gram', $metalRate->rate_per_10_gram) }}" 
                                           step="0.01" min="0" readonly
                                           class="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                                    <p class="mt-1 text-sm text-gray-500">Automatically calculated from rate per gram</p>
                                </div>
                            </div>
                        </div>

                        <!-- Date and Status Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Date & Status Information</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="effective_date" class="block text-sm font-medium text-gray-700">Effective Date *</label>
                                    <input type="date" name="effective_date" id="effective_date" value="{{ old('effective_date', $metalRate->effective_date->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('effective_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div class="flex items-center">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" name="is_active" id="is_active" value="1" 
                                               {{ old('is_active', $metalRate->is_active) ? 'checked' : '' }}
                                               class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="is_active" class="font-medium text-gray-700">Set as Active Rate</label>
                                        <p class="text-gray-500">This will deactivate other rates for the same metal/purity</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4">
                            <a href="{{ route('metal-rates.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Metal Rate
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const ratePerGramInput = document.getElementById('rate_per_gram');
            const ratePer10GramInput = document.getElementById('rate_per_10_gram');

            // Purity options based on metal type
            const purityOptions = {
                'Gold': ['22K', '18K', '14K'],
                'Silver': ['925', '999'],
                'Platinum': ['950', '900']
            };

            // Current values for pre-selection
            const currentPurity = '{{ old('purity', $metalRate->purity) }}';

            // Update purity options when metal type changes
            metalTypeSelect.addEventListener('change', function() {
                updatePurityOptions();
            });

            function updatePurityOptions() {
                const metalType = metalTypeSelect.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';
                
                if (metalType && purityOptions[metalType]) {
                    purityOptions[metalType].forEach(function(purity) {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        if (purity === currentPurity) {
                            option.selected = true;
                        }
                        puritySelect.appendChild(option);
                    });
                }
            }

            // Initialize purity options on page load
            updatePurityOptions();

            // Auto-calculate rate per 10 grams
            ratePerGramInput.addEventListener('input', function() {
                const ratePerGram = parseFloat(this.value) || 0;
                ratePer10GramInput.value = (ratePerGram * 10).toFixed(2);
            });

            // Initialize rate per 10 grams calculation
            if (ratePerGramInput.value) {
                const ratePerGram = parseFloat(ratePerGramInput.value) || 0;
                ratePer10GramInput.value = (ratePerGram * 10).toFixed(2);
            }
        });
    </script>
</x-app-layout>
