<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Metal Rates Management') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('calculator') }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Calculator
                </a>
                @can('create_metal_rate')
                    <a href="{{ route('metal-rates.create') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Add New Rate
                    </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Current Active Rates -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Active Rates</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @forelse($currentRates as $metalType => $purities)
                            <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
                                <h4 class="text-lg font-semibold text-gray-900 mb-3">{{ $metalType }}</h4>
                                @foreach($purities as $purity => $rate)
                                    <div class="flex justify-between items-center mb-2">
                                        <span class="text-sm font-medium text-gray-700">{{ $purity }}</span>
                                        <div class="text-right">
                                            <div class="text-lg font-bold text-gray-900">₹{{ number_format($rate->rate_per_gram, 2) }}/g</div>
                                            <div class="text-xs text-gray-500">₹{{ number_format($rate->rate_per_10_gram, 2) }}/10g</div>
                                        </div>
                                    </div>
                                @endforeach
                                <div class="text-xs text-gray-500 mt-2">
                                    Updated: {{ $purities->first()->effective_date->format('d M, Y') }}
                                </div>
                            </div>
                        @empty
                            <div class="col-span-3 text-center text-gray-500 py-8">
                                No active rates found. Please add some metal rates.
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('metal-rates.index') }}" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                        <div>
                            <select name="metal_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Metals</option>
                                <option value="Gold" {{ request('metal_type') === 'Gold' ? 'selected' : '' }}>Gold</option>
                                <option value="Silver" {{ request('metal_type') === 'Silver' ? 'selected' : '' }}>Silver</option>
                                <option value="Platinum" {{ request('metal_type') === 'Platinum' ? 'selected' : '' }}>Platinum</option>
                            </select>
                        </div>
                        <div>
                            <select name="purity" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Purities</option>
                                <option value="22K" {{ request('purity') === '22K' ? 'selected' : '' }}>22K</option>
                                <option value="18K" {{ request('purity') === '18K' ? 'selected' : '' }}>18K</option>
                                <option value="14K" {{ request('purity') === '14K' ? 'selected' : '' }}>14K</option>
                                <option value="925" {{ request('purity') === '925' ? 'selected' : '' }}>925</option>
                                <option value="999" {{ request('purity') === '999' ? 'selected' : '' }}>999</option>
                                <option value="950" {{ request('purity') === '950' ? 'selected' : '' }}>950</option>
                            </select>
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        <div>
                            <input type="date" name="from_date" value="{{ request('from_date') }}" 
                                   placeholder="From Date"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Search
                            </button>
                            <a href="{{ route('metal-rates.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Update Form -->
            @can('bulk_manage_metal_rates')
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Rate Update</h3>
                        <form method="POST" action="{{ route('metal-rates.bulk-update') }}" id="bulkUpdateForm">
                            @csrf
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                                <div>
                                    <label for="effective_date" class="block text-sm font-medium text-gray-700">Effective Date</label>
                                    <input type="date" name="effective_date" id="effective_date" value="{{ today()->format('Y-m-d') }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                </div>
                                <div class="flex items-end">
                                    <button type="button" id="addRateRow" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                        Add Rate
                                    </button>
                                </div>
                            </div>
                            
                            <div id="ratesContainer" class="space-y-3">
                                <!-- Rate rows will be added here -->
                            </div>
                            
                            <div class="mt-4">
                                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Update All Rates
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            @endcan

            <!-- Rates History Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Rate History</h3>

                        <!-- Bulk Actions -->
                        <div id="bulkActions" class="hidden flex items-center space-x-2">
                            <span class="text-sm text-gray-600">
                                <span id="selectedCount">0</span> selected
                            </span>
                            @can('bulk_manage_metal_rates')
                                <button type="button" id="bulkActivate" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Activate Selected
                                </button>
                                <button type="button" id="bulkDeactivate" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-orange-700 bg-orange-100 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                    </svg>
                                    Deactivate Selected
                                </button>
                            @endcan
                            @can('delete_metal_rate')
                                <button type="button" id="bulkDelete" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Delete Selected
                                </button>
                            @endcan
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left">
                                        <input type="checkbox" id="selectAll" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metal & Purity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per Gram</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate per 10g</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($rates as $rate)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" name="selected_rates[]" value="{{ $rate->id }}" class="rate-checkbox focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">{{ $rate->metal_type }}</div>
                                            <div class="text-sm text-gray-500">{{ $rate->purity }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">₹{{ number_format($rate->rate_per_gram, 2) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">₹{{ number_format($rate->rate_per_10_gram, 2) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $rate->effective_date->format('d M, Y') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $rate->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                                {{ $rate->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $rate->createdBy->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $rate->created_at->format('d M, Y') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                @can('view_metal_rates')
                                                    <a href="{{ route('metal-rates.show', $rate) }}"
                                                       class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                                       title="View Details">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                        </svg>
                                                        View
                                                    </a>
                                                @endcan
                                                @can('edit_metal_rate')
                                                    <a href="{{ route('metal-rates.edit', $rate) }}"
                                                       class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                                       title="Edit Rate">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                        Edit
                                                    </a>
                                                @endcan
                                                @can('toggle_metal_rate_status')
                                                    <form method="POST" action="{{ route('metal-rates.toggle-active', $rate) }}" class="inline">
                                                        @csrf
                                                        <button type="submit"
                                                                class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded {{ $rate->is_active ? 'text-orange-700 bg-orange-100 hover:bg-orange-200 focus:ring-orange-500' : 'text-green-700 bg-green-100 hover:bg-green-200 focus:ring-green-500' }} focus:outline-none focus:ring-2 focus:ring-offset-2"
                                                                title="{{ $rate->is_active ? 'Deactivate Rate' : 'Activate Rate' }}">
                                                            @if($rate->is_active)
                                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                                </svg>
                                                                Deactivate
                                                            @else
                                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                Activate
                                                            @endif
                                                        </button>
                                                    </form>
                                                @endcan
                                                @can('delete_metal_rate')
                                                    <form method="POST" action="{{ route('metal-rates.destroy', $rate) }}" class="inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this metal rate? This action cannot be undone.')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit"
                                                                class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                                                title="Delete Rate">
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                            </svg>
                                                            Delete
                                                        </button>
                                                    </form>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                            No metal rates found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $rates->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const addRateRowButton = document.getElementById('addRateRow');
            const ratesContainer = document.getElementById('ratesContainer');
            let rateIndex = 0;

            // Bulk actions functionality
            const selectAllCheckbox = document.getElementById('selectAll');
            const rateCheckboxes = document.querySelectorAll('.rate-checkbox');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.getElementById('selectedCount');
            const bulkActivate = document.getElementById('bulkActivate');
            const bulkDeactivate = document.getElementById('bulkDeactivate');
            const bulkDelete = document.getElementById('bulkDelete');

            // Select all functionality
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    rateCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkActions();
                });
            }

            // Individual checkbox functionality
            rateCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBulkActions);
            });

            function updateBulkActions() {
                const checkedBoxes = document.querySelectorAll('.rate-checkbox:checked');
                const count = checkedBoxes.length;

                if (count > 0) {
                    bulkActions.classList.remove('hidden');
                    selectedCount.textContent = count;
                } else {
                    bulkActions.classList.add('hidden');
                }

                // Update select all checkbox state
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = count === rateCheckboxes.length;
                    selectAllCheckbox.indeterminate = count > 0 && count < rateCheckboxes.length;
                }
            }

            // Bulk activate
            if (bulkActivate) {
                bulkActivate.addEventListener('click', function() {
                    const selectedIds = getSelectedIds();
                    if (selectedIds.length > 0 && confirm(`Are you sure you want to activate ${selectedIds.length} selected rate(s)?`)) {
                        performBulkAction('activate', selectedIds);
                    }
                });
            }

            // Bulk deactivate
            if (bulkDeactivate) {
                bulkDeactivate.addEventListener('click', function() {
                    const selectedIds = getSelectedIds();
                    if (selectedIds.length > 0 && confirm(`Are you sure you want to deactivate ${selectedIds.length} selected rate(s)?`)) {
                        performBulkAction('deactivate', selectedIds);
                    }
                });
            }

            // Bulk delete
            if (bulkDelete) {
                bulkDelete.addEventListener('click', function() {
                    const selectedIds = getSelectedIds();
                    if (selectedIds.length > 0 && confirm(`Are you sure you want to delete ${selectedIds.length} selected rate(s)? This action cannot be undone.`)) {
                        performBulkAction('delete', selectedIds);
                    }
                });
            }

            function getSelectedIds() {
                return Array.from(document.querySelectorAll('.rate-checkbox:checked')).map(cb => cb.value);
            }

            function performBulkAction(action, ids) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route("metal-rates.bulk-action") }}';

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = action;
                form.appendChild(actionInput);

                ids.forEach(id => {
                    const idInput = document.createElement('input');
                    idInput.type = 'hidden';
                    idInput.name = 'ids[]';
                    idInput.value = id;
                    form.appendChild(idInput);
                });

                document.body.appendChild(form);
                form.submit();
            }

            addRateRowButton.addEventListener('click', function() {
                const rateRow = document.createElement('div');
                rateRow.className = 'grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg';
                rateRow.innerHTML = `
                    <div>
                        <select name="rates[${rateIndex}][metal_type]" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="">Select Metal</option>
                            <option value="Gold">Gold</option>
                            <option value="Silver">Silver</option>
                            <option value="Platinum">Platinum</option>
                        </select>
                    </div>
                    <div>
                        <select name="rates[${rateIndex}][purity]" required class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="">Select Purity</option>
                            <option value="22K">22K</option>
                            <option value="18K">18K</option>
                            <option value="14K">14K</option>
                            <option value="925">925</option>
                            <option value="999">999</option>
                            <option value="950">950</option>
                            <option value="900">900</option>
                        </select>
                    </div>
                    <div>
                        <input type="number" name="rates[${rateIndex}][rate_per_gram]" placeholder="Rate per gram" step="0.01" min="0" required
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    </div>
                    <div class="flex items-center">
                        <button type="button" class="remove-rate bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            Remove
                        </button>
                    </div>
                `;

                rateRow.querySelector('.remove-rate').addEventListener('click', function() {
                    rateRow.remove();
                });

                ratesContainer.appendChild(rateRow);
                rateIndex++;
            });

            // Add initial rate row
            addRateRowButton.click();
        });
    </script>
</x-app-layout>
