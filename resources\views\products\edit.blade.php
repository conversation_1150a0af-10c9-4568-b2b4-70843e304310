<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Product') }}
            </h2>
            <a href="{{ route('products.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Products
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form action="{{ route('products.update', $product) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Basic Information -->
                            <div class="md:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            </div>

                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Product Name *</label>
                                <input type="text" name="name" id="name" value="{{ old('name', $product->name) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="category_id" class="block text-sm font-medium text-gray-700">Category *</label>
                                <select name="category_id" id="category_id" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="">Select Category</option>
                                    @foreach($categories as $category)
                                        @if($category->isParent())
                                            <optgroup label="{{ $category->name }}">
                                                <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                                    {{ $category->name }}
                                                </option>
                                                @foreach($category->children as $child)
                                                    <option value="{{ $child->id }}" {{ old('category_id', $product->category_id) == $child->id ? 'selected' : '' }}>
                                                        &nbsp;&nbsp;{{ $child->name }}
                                                    </option>
                                                @endforeach
                                            </optgroup>
                                        @endif
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>



                            <div>
                                <label for="size" class="block text-sm font-medium text-gray-700">Size</label>
                                <input type="text" name="size" id="size" value="{{ old('size', $product->size) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('size')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Metal Information -->
                            <div class="md:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Metal Information</h3>
                            </div>

                            <div>
                                <label for="metal_type" class="block text-sm font-medium text-gray-700">Metal Type *</label>
                                <select name="metal_type" id="metal_type" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="">Select Metal Type</option>
                                    <option value="Gold" {{ old('metal_type', $product->metal_type) == 'Gold' ? 'selected' : '' }}>Gold</option>
                                    <option value="Silver" {{ old('metal_type', $product->metal_type) == 'Silver' ? 'selected' : '' }}>Silver</option>
                                    <option value="Platinum" {{ old('metal_type', $product->metal_type) == 'Platinum' ? 'selected' : '' }}>Platinum</option>
                                </select>
                                @error('metal_type')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="purity" class="block text-sm font-medium text-gray-700">Purity *</label>
                                <select name="purity" id="purity" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="">Select Purity</option>
                                </select>
                                @error('purity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="gross_weight" class="block text-sm font-medium text-gray-700">Gross Weight (g) *</label>
                                <input type="number" name="gross_weight" id="gross_weight" step="0.001" min="0" value="{{ old('gross_weight', $product->gross_weight) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('gross_weight')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="net_weight" class="block text-sm font-medium text-gray-700">Net Weight (g) *</label>
                                <input type="number" name="net_weight" id="net_weight" step="0.001" min="0" value="{{ old('net_weight', $product->net_weight) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('net_weight')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="wastage_percentage" class="block text-sm font-medium text-gray-700">Wastage Percentage (%)</label>
                                <input type="number" name="wastage_percentage" id="wastage_percentage" step="0.01" min="0" max="100" value="{{ old('wastage_percentage', $product->wastage_percentage) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('wastage_percentage')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="making_charges" class="block text-sm font-medium text-gray-700">Making Charges (₹) *</label>
                                <input type="number" name="making_charges" id="making_charges" step="0.01" min="0" value="{{ old('making_charges', $product->making_charges) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('making_charges')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Stone Information -->
                            <div class="md:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Stone Information</h3>
                            </div>

                            <div class="md:col-span-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="has_stones" id="has_stones" value="1" {{ old('has_stones', $product->has_stones) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">This product has stones</span>
                                </label>
                            </div>

                            <div id="stone-details" class="md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6" style="display: none;">
                                <div>
                                    <label for="stone_weight" class="block text-sm font-medium text-gray-700">Stone Weight (g)</label>
                                    <input type="number" name="stone_weight" id="stone_weight" step="0.001" min="0" value="{{ old('stone_weight', $product->stone_weight) }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('stone_weight')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="stone_charges" class="block text-sm font-medium text-gray-700">Stone Charges (₹)</label>
                                    <input type="number" name="stone_charges" id="stone_charges" step="0.01" min="0" value="{{ old('stone_charges', $product->stone_charges) }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    @error('stone_charges')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Pricing Information -->
                            <div class="md:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Pricing & Inventory</h3>
                            </div>

                            <div>
                                <label for="cost_price" class="block text-sm font-medium text-gray-700">Cost Price (₹) *</label>
                                <input type="number" name="cost_price" id="cost_price" step="0.01" min="0" value="{{ old('cost_price', $product->cost_price) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('cost_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="selling_price" class="block text-sm font-medium text-gray-700">Selling Price (₹) *</label>
                                <input type="number" name="selling_price" id="selling_price" step="0.01" min="0" value="{{ old('selling_price', $product->selling_price) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('selling_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="quantity" class="block text-sm font-medium text-gray-700">Quantity *</label>
                                <input type="number" name="quantity" id="quantity" min="0" value="{{ old('quantity', $product->quantity) }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('quantity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700">Status *</label>
                                <select name="status" id="status" required
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <option value="in_stock" {{ old('status', $product->status) == 'in_stock' ? 'selected' : '' }}>In Stock</option>
                                    <option value="sold" {{ old('status', $product->status) == 'sold' ? 'selected' : '' }}>Sold</option>
                                    <option value="reserved" {{ old('status', $product->status) == 'reserved' ? 'selected' : '' }}>Reserved</option>
                                    <option value="repair" {{ old('status', $product->status) == 'repair' ? 'selected' : '' }}>Under Repair</option>
                                </select>
                                @error('status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Additional Information -->
                            <div class="md:col-span-2">
                                <h3 class="text-lg font-medium text-gray-900 mb-4 mt-6">Additional Information</h3>
                            </div>

                            <div>
                                <label for="hsn_code" class="block text-sm font-medium text-gray-700">HSN Code</label>
                                <input type="text" name="hsn_code" id="hsn_code" value="{{ old('hsn_code', $product->hsn_code) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('hsn_code')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="huid_number" class="block text-sm font-medium text-gray-700">
                                    HUID Number 
                                    <span class="text-xs text-gray-500">(Hallmark Unique ID)</span>
                                </label>
                                <input type="text" name="huid_number" id="huid_number" value="{{ old('huid_number', $product->huid_number) }}"
                                       placeholder="6-character HUID (e.g., A00001, Z99999, M1N2O3)"
                                       maxlength="6"
                                       style="text-transform: uppercase;"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <p class="mt-1 text-xs text-gray-500">
                                    <span id="huid-requirement-text">HUID is mandatory for gold jewelry above 2g and silver above 5g as per BIS regulations</span>
                                </p>
                                @error('huid_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                @error('huid_compliance')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="tag_number" class="block text-sm font-medium text-gray-700">
                                    Tag Number
                                    <span class="text-xs text-gray-500">(Indian Jewellery Standard)</span>
                                </label>
                                <input type="text" name="tag_number" id="tag_number" value="{{ old('tag_number', $product->tag_number) }}"
                                       placeholder="Leave empty to auto-generate (e.g., RG24000001G22)"
                                       maxlength="20"
                                       style="text-transform: uppercase;"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <p class="mt-1 text-xs text-gray-500">
                                    Format: {Category}{Year}{Sequence}{Metal} - Auto-generated based on category, metal type, and purity
                                </p>
                                @error('tag_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="barcode" class="block text-sm font-medium text-gray-700">Barcode</label>
                                <input type="text" name="barcode" id="barcode" value="{{ old('barcode', $product->barcode) }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                @error('barcode')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="md:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                <textarea name="description" id="description" rows="3"
                                          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">{{ old('description', $product->description) }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="md:col-span-2">
                                <label for="image" class="block text-sm font-medium text-gray-700">Product Image</label>
                                @if($product->image_path)
                                    <div class="mb-2">
                                        <img src="{{ Storage::url($product->image_path) }}" alt="Current image" class="w-20 h-20 object-cover rounded">
                                        <p class="text-sm text-gray-500">Current image</p>
                                    </div>
                                @endif
                                <input type="file" name="image" id="image" accept=".jpg,.jpeg,.png"
                                       class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                <p class="mt-1 text-sm text-gray-500">Upload JPG, JPEG, or PNG files (max 2MB). Leave empty to keep current image.</p>
                                @error('image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="flex items-center justify-end mt-6 space-x-3">
                            <a href="{{ route('products.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Product
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const metalTypeSelect = document.getElementById('metal_type');
            const puritySelect = document.getElementById('purity');
            const hasStonesCheckbox = document.getElementById('has_stones');
            const stoneDetails = document.getElementById('stone-details');
            const netWeightInput = document.getElementById('net_weight');
            const huidInput = document.getElementById('huid_number');
            const huidRequirementText = document.getElementById('huid-requirement-text');

            const purityOptions = {
                'Gold': ['14K', '18K', '22K', '24K'],
                'Silver': ['925', '999'],
                'Platinum': ['950', '999']
            };

            // Current values for initialization
            const currentPurity = '{{ old('purity', $product->purity) }}';

            // Update purity options based on metal type
            metalTypeSelect.addEventListener('change', function() {
                const selectedMetal = this.value;
                puritySelect.innerHTML = '<option value="">Select Purity</option>';

                if (selectedMetal && purityOptions[selectedMetal]) {
                    purityOptions[selectedMetal].forEach(purity => {
                        const option = document.createElement('option');
                        option.value = purity;
                        option.textContent = purity;
                        if (purity === currentPurity) {
                            option.selected = true;
                        }
                        puritySelect.appendChild(option);
                    });
                }

                checkHuidRequirement();
            });

            // Show/hide stone details
            hasStonesCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    stoneDetails.style.display = 'grid';
                } else {
                    stoneDetails.style.display = 'none';
                }
            });

            // Initialize stone details visibility
            if (hasStonesCheckbox.checked) {
                stoneDetails.style.display = 'grid';
            }

            // Initialize purity options if metal type is already selected
            if (metalTypeSelect.value) {
                metalTypeSelect.dispatchEvent(new Event('change'));
            }

            // HUID validation and requirement checking
            function checkHuidRequirement() {
                const metalType = metalTypeSelect.value;
                const netWeight = parseFloat(netWeightInput.value) || 0;

                let isRequired = false;
                let message = '';

                if (metalType === 'Gold' && netWeight > 2) {
                    isRequired = true;
                    message = `⚠️ HUID is MANDATORY for gold jewelry above 2g (Current: ${netWeight}g)`;
                    huidRequirementText.className = 'mt-1 text-xs text-red-600 font-medium';
                } else if (metalType === 'Silver' && netWeight > 5) {
                    isRequired = true;
                    message = `⚠️ HUID is MANDATORY for silver jewelry above 5g (Current: ${netWeight}g)`;
                    huidRequirementText.className = 'mt-1 text-xs text-red-600 font-medium';
                } else {
                    message = 'HUID is not mandatory for this item but recommended for authenticity';
                    huidRequirementText.className = 'mt-1 text-xs text-gray-500';
                }

                huidRequirementText.textContent = message;

                // Update HUID field styling
                if (isRequired) {
                    huidInput.classList.add('border-red-300');
                    huidInput.classList.remove('border-gray-300');
                } else {
                    huidInput.classList.remove('border-red-300');
                    huidInput.classList.add('border-gray-300');
                }
            }

            // HUID format validation
            function validateHuidFormat() {
                let huid = huidInput.value.toUpperCase().replace(/[^A-Z0-9]/g, '');

                // Update the input value to show uppercase
                huidInput.value = huid;

                if (huid.length === 0) return;

                if (huid.length !== 6) {
                    huidInput.setCustomValidity('HUID must be exactly 6 characters (A-Z, 0-9)');
                } else if (!/^[A-Z0-9]{6}$/.test(huid)) {
                    huidInput.setCustomValidity('HUID must contain only letters (A-Z) and numbers (0-9)');
                } else {
                    huidInput.setCustomValidity('');
                }
            }

            metalTypeSelect.addEventListener('change', checkHuidRequirement);
            netWeightInput.addEventListener('input', checkHuidRequirement);
            huidInput.addEventListener('input', validateHuidFormat);



            // Initialize purity options on page load
            if (metalTypeSelect.value) {
                metalTypeSelect.dispatchEvent(new Event('change'));
            }

            // Initial check
            checkHuidRequirement();
        });
    </script>
    @endpush
</x-app-layout>
