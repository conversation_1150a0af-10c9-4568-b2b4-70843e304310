<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Inventory Management') }}
            </h2>
            <div class="flex items-center space-x-3">
                @can('create_product')
                <a href="{{ route('products.create') }}"
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Product
                </a>
                @endcan

                <!-- Bulk Actions Button -->
                @can('bulk_manage_products')
                <button id="bulkActionsBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center" style="display: none;">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Bulk Actions
                </button>
                @endcan

                <!-- Inventory Report Link -->
                @can('view_inventory')
                <a href="{{ route('inventory.report') }}"
                   class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Inventory Report
                </a>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif

            <!-- Inventory Alerts -->
            @if($lowStockCount > 0 || $huidRequiredCount > 0)
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-yellow-800">Inventory Alerts</h3>
                </div>
                <div class="mt-2 flex flex-wrap gap-4">
                    @if($lowStockCount > 0)
                    <div class="flex items-center text-sm text-yellow-700">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        <span class="font-medium">{{ $lowStockCount }}</span> products with low stock (≤5 units)
                    </div>
                    @endif
                    @if($huidRequiredCount > 0)
                    <div class="flex items-center text-sm text-yellow-700">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <span class="font-medium">{{ $huidRequiredCount }}</span> products missing required HUID
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Search and Filter -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('products.index') }}" class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <input type="text" name="search" value="{{ request('search') }}" 
                                   placeholder="Search by name, barcode, HUID, tag number..."
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>
                        <div>
                            <select name="category" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Categories</option>
                                @foreach($categories as $category)
                                    @if($category->isParent())
                                        <optgroup label="{{ $category->name }}">
                                            <option value="{{ $category->name }}" {{ request('category') === $category->name ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                            @foreach($category->children as $child)
                                                <option value="{{ $child->name }}" {{ request('category') === $child->name ? 'selected' : '' }}>
                                                    &nbsp;&nbsp;{{ $child->name }}
                                                </option>
                                            @endforeach
                                        </optgroup>
                                    @endif
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <select name="metal_type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Metals</option>
                                @foreach($metalTypes as $metalType)
                                    <option value="{{ $metalType }}" {{ request('metal_type') === $metalType ? 'selected' : '' }}>{{ $metalType }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div>
                            <select name="status" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Status</option>
                                <option value="in_stock" {{ request('status') === 'in_stock' ? 'selected' : '' }}>In Stock</option>
                                <option value="sold" {{ request('status') === 'sold' ? 'selected' : '' }}>Sold</option>
                                <option value="reserved" {{ request('status') === 'reserved' ? 'selected' : '' }}>Reserved</option>
                                <option value="repair" {{ request('status') === 'repair' ? 'selected' : '' }}>In Repair</option>
                            </select>
                        </div>
                        <div>
                            <select name="huid_filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All HUID</option>
                                <option value="with_huid" {{ request('huid_filter') === 'with_huid' ? 'selected' : '' }}>With HUID</option>
                                <option value="without_huid" {{ request('huid_filter') === 'without_huid' ? 'selected' : '' }}>Without HUID</option>
                                <option value="huid_required" {{ request('huid_filter') === 'huid_required' ? 'selected' : '' }}>HUID Required</option>
                            </select>
                        </div>
                        <div class="flex space-x-2">
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded flex-1">
                                Filter
                            </button>
                            <a href="{{ route('products.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    @can('bulk_manage_products')
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    </th>
                                    @endcan
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metal/Purity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weight</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HUID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tag Number</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($products as $product)
                                    <tr>
                                        @can('bulk_manage_products')
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" name="product_ids[]" value="{{ $product->id }}" class="product-checkbox rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                        </td>
                                        @endcan
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                @if($product->image_path)
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <img class="h-10 w-10 rounded-full object-cover" src="{{ asset('storage/' . $product->image_path) }}" alt="{{ $product->name }}">
                                                    </div>
                                                @endif
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">{{ $product->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $product->barcode }}</div>
                                                    @if($product->huid_number)
                                                        <div class="text-xs text-green-600 font-medium">✓ HUID: {{ $product->huid_number }}</div>
                                                    @elseif($product->huid_required)
                                                        <div class="text-xs text-red-600 font-medium">⚠️ HUID Required</div>
                                                    @else
                                                        <div class="text-xs text-gray-400">HUID not required</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $product->category }}</div>
                                            @if($product->subcategory)
                                                <div class="text-sm text-gray-500">{{ $product->subcategory }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $product->metal_type }}</div>
                                            <div class="text-sm text-gray-500">{{ $product->purity }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $product->gross_weight }}g</div>
                                            <div class="text-sm text-gray-500">Net: {{ $product->net_weight }}g</div>
                                            @if($product->stone_weight > 0)
                                                <div class="text-xs text-purple-600">Stone: {{ $product->stone_weight }}ct</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($product->huid_number)
                                                <div class="text-sm font-mono text-gray-900">{{ $product->huid_number }}</div>
                                                <div class="text-xs text-green-600">✓ Compliant</div>
                                            @elseif($product->huid_required)
                                                <div class="text-xs text-red-600 font-medium">⚠️ Required</div>
                                            @else
                                                <div class="text-xs text-gray-400">Not required</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($product->tag_number)
                                                <div class="text-sm font-mono text-gray-900">{{ $product->tag_number }}</div>
                                                <div class="text-xs text-blue-600">{{ $product->formatted_tag_number }}</div>
                                            @else
                                                <div class="text-xs text-gray-400">Not assigned</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">₹{{ number_format($product->selling_price, 2) }}</div>
                                            <div class="text-sm text-gray-500">Cost: ₹{{ number_format($product->cost_price, 2) }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $product->quantity }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                {{ $product->status === 'in_stock' ? 'bg-green-100 text-green-800' : 
                                                   ($product->status === 'sold' ? 'bg-red-100 text-red-800' : 
                                                   ($product->status === 'reserved' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800')) }}">
                                                {{ ucfirst(str_replace('_', ' ', $product->status)) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                @can('view_inventory')
                                                    <a href="{{ route('products.show', $product) }}"
                                                       class="text-indigo-600 hover:text-indigo-900 flex items-center" title="View Details">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                        </svg>
                                                    </a>
                                                @endcan
                                                @can('edit_product')
                                                    <a href="{{ route('products.edit', $product) }}"
                                                       class="text-blue-600 hover:text-blue-900 flex items-center" title="Edit Product">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                    </a>
                                                @endcan

                                                <!-- Status Toggle -->
                                                @if($product->status === 'in_stock' || $product->status === 'sold')
                                                <form method="POST" action="{{ route('products.toggle-status', $product) }}" class="inline">
                                                    @csrf
                                                    <button type="submit"
                                                            class="{{ $product->status === 'in_stock' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900' }} flex items-center"
                                                            title="{{ $product->status === 'in_stock' ? 'Mark as Sold' : 'Mark as In Stock' }}">
                                                        @if($product->status === 'in_stock')
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                            </svg>
                                                        @else
                                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                                            </svg>
                                                        @endif
                                                    </button>
                                                </form>
                                                @endif

                                                @can('manage_barcode')
                                                    <a href="{{ route('products.barcode', $product) }}"
                                                       class="text-purple-600 hover:text-purple-900 flex items-center" title="Generate Barcode">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1zm12 0h2a1 1 0 001-1V6a1 1 0 00-1-1h-2a1 1 0 00-1 1v1a1 1 0 001 1zM5 20h2a1 1 0 001-1v-1a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                                                        </svg>
                                                    </a>
                                                @endcan
                                                @can('delete_product')
                                                    <form method="POST" action="{{ route('products.destroy', $product) }}" class="inline" 
                                                          onsubmit="return confirm('Are you sure you want to delete this product?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                                    </form>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                            No products found.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-4">
                        {{ $products->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    @can('bulk_manage_products')
    <div id="bulkActionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Bulk Actions</h3>
                <form id="bulkActionForm" method="POST" action="{{ route('products.bulk-action') }}">
                    @csrf
                    <input type="hidden" name="product_ids" id="selectedProductIds">

                    <div class="mb-4">
                        <label for="bulkAction" class="block text-sm font-medium text-gray-700 mb-2">Select Action:</label>
                        <select name="action" id="bulkAction" class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                            <option value="">Choose an action...</option>
                            <option value="status_change">Change Status</option>
                            <option value="delete">Delete Products</option>
                        </select>
                    </div>

                    <div id="statusOptions" class="mb-4 hidden">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">New Status:</label>
                        <select name="status" id="status" class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="in_stock">In Stock</option>
                            <option value="sold">Sold</option>
                            <option value="reserved">Reserved</option>
                            <option value="repair">Under Repair</option>
                        </select>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancelBulkAction" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                            Execute Action
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    @endcan

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const productCheckboxes = document.querySelectorAll('.product-checkbox');
            const bulkActionsBtn = document.getElementById('bulkActionsBtn');
            const bulkActionsModal = document.getElementById('bulkActionsModal');
            const bulkActionForm = document.getElementById('bulkActionForm');
            const bulkActionSelect = document.getElementById('bulkAction');
            const statusOptions = document.getElementById('statusOptions');
            const cancelBulkAction = document.getElementById('cancelBulkAction');

            // Handle select all checkbox
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    productCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    toggleBulkActionsButton();
                });
            }

            // Handle individual checkboxes
            productCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = checkedBoxes.length === productCheckboxes.length;
                        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < productCheckboxes.length;
                    }
                    toggleBulkActionsButton();
                });
            });

            // Toggle bulk actions button visibility
            function toggleBulkActionsButton() {
                const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
                if (bulkActionsBtn) {
                    bulkActionsBtn.style.display = checkedBoxes.length > 0 ? 'flex' : 'none';
                }
            }

            // Show bulk actions modal
            if (bulkActionsBtn) {
                bulkActionsBtn.addEventListener('click', function() {
                    const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
                    const productIds = Array.from(checkedBoxes).map(cb => cb.value);
                    document.getElementById('selectedProductIds').value = JSON.stringify(productIds);
                    bulkActionsModal.classList.remove('hidden');
                });
            }

            // Handle bulk action selection
            if (bulkActionSelect) {
                bulkActionSelect.addEventListener('change', function() {
                    if (this.value === 'status_change') {
                        statusOptions.classList.remove('hidden');
                    } else {
                        statusOptions.classList.add('hidden');
                    }
                });
            }

            // Cancel bulk action
            if (cancelBulkAction) {
                cancelBulkAction.addEventListener('click', function() {
                    bulkActionsModal.classList.add('hidden');
                    bulkActionForm.reset();
                    statusOptions.classList.add('hidden');
                });
            }

            // Close modal when clicking outside
            if (bulkActionsModal) {
                bulkActionsModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.classList.add('hidden');
                        bulkActionForm.reset();
                        statusOptions.classList.add('hidden');
                    }
                });
            }

            // Handle form submission
            if (bulkActionForm) {
                bulkActionForm.addEventListener('submit', function(e) {
                    const action = bulkActionSelect.value;
                    const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');

                    if (checkedBoxes.length === 0) {
                        e.preventDefault();
                        alert('Please select at least one product.');
                        return;
                    }

                    let confirmMessage = '';
                    if (action === 'delete') {
                        confirmMessage = `Are you sure you want to delete ${checkedBoxes.length} product(s)? This action cannot be undone.`;
                    } else if (action === 'status_change') {
                        const status = document.getElementById('status').value;
                        const statusLabel = status.replace('_', ' ');
                        confirmMessage = `Are you sure you want to change the status of ${checkedBoxes.length} product(s) to "${statusLabel}"?`;
                    }

                    if (confirmMessage && !confirm(confirmMessage)) {
                        e.preventDefault();
                    }
                });
            }
        });
    </script>
</x-app-layout>
