<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Product Details') }} - {{ $product->name }}
            </h2>
            <div class="flex items-center space-x-3">
                @can('edit_product')
                <a href="{{ route('products.edit', $product) }}"
                   class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Product
                </a>
                @endcan

                <!-- Status Toggle -->
                @if($product->status === 'in_stock' || $product->status === 'sold')
                <form method="POST" action="{{ route('products.toggle-status', $product) }}" class="inline">
                    @csrf
                    <button type="submit"
                            class="{{ $product->status === 'in_stock' ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700' }} text-white px-4 py-2 rounded-lg flex items-center">
                        @if($product->status === 'in_stock')
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            Mark as Sold
                        @else
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            Mark as In Stock
                        @endif
                    </button>
                </form>
                @endif

                <!-- Quantity Adjustment Button -->
                <button id="adjustQuantityBtn"
                        class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4zM9 6v10h6V6H9z"></path>
                    </svg>
                    Adjust Quantity
                </button>

                @can('manage_barcode')
                <a href="{{ route('products.barcode', $product) }}"
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1zm12 0h2a1 1 0 001-1V6a1 1 0 00-1-1h-2a1 1 0 00-1 1v1a1 1 0 001 1zM5 20h2a1 1 0 001-1v-1a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                    </svg>
                    Print Barcode
                </a>
                @endcan

                <a href="{{ route('products.index') }}"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Inventory
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Product Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Product Image -->
                        <div class="lg:col-span-1">
                            @if($product->image_path)
                                <img src="{{ asset('storage/' . $product->image_path) }}" alt="{{ $product->name }}" 
                                     class="w-full h-64 object-cover rounded-lg shadow-md">
                            @else
                                <div class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <span class="text-gray-500">No Image</span>
                                </div>
                            @endif
                            
                            <!-- Status Badge -->
                            <div class="mt-4">
                                <span class="px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full 
                                    {{ $product->status === 'in_stock' ? 'bg-green-100 text-green-800' : 
                                       ($product->status === 'sold' ? 'bg-red-100 text-red-800' : 
                                       ($product->status === 'reserved' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800')) }}">
                                    {{ ucfirst(str_replace('_', ' ', $product->status)) }}
                                </span>
                            </div>
                        </div>

                        <!-- Product Details -->
                        <div class="lg:col-span-2">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Basic Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Product Name</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->name }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Category</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->category }}</dd>
                                        </div>
                                        @if($product->subcategory)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Subcategory</dt>
                                                <dd class="text-sm text-gray-900">{{ $product->subcategory }}</dd>
                                            </div>
                                        @endif
                                        @if($product->size)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Size</dt>
                                                <dd class="text-sm text-gray-900">{{ $product->size }}</dd>
                                            </div>
                                        @endif
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Quantity</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->quantity }}</dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Metal Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Metal Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Metal Type</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->metal_type }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Purity</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->purity }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Gross Weight</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->gross_weight }} grams</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Net Weight</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->net_weight }} grams</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Wastage</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->wastage_percentage }}% ({{ number_format($product->wastage_amount, 3) }}g)</dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Stone Info -->
                                @if($product->has_stones)
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-4">Stone Information</h3>
                                        <dl class="space-y-2">
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Stone Weight</dt>
                                                <dd class="text-sm text-gray-900">{{ $product->stone_weight }} carats</dd>
                                            </div>
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">Stone Charges</dt>
                                                <dd class="text-sm text-gray-900">₹{{ number_format($product->stone_charges, 2) }}</dd>
                                            </div>
                                        </dl>
                                    </div>
                                @endif

                                <!-- Pricing Info -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing Information</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Making Charges</dt>
                                            <dd class="text-sm text-gray-900">₹{{ number_format($product->making_charges, 2) }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Cost Price</dt>
                                            <dd class="text-sm text-gray-900">₹{{ number_format($product->cost_price, 2) }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Selling Price</dt>
                                            <dd class="text-sm text-gray-900 font-semibold">₹{{ number_format($product->selling_price, 2) }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Profit</dt>
                                            <dd class="text-sm {{ $product->profit >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                                ₹{{ number_format($product->profit, 2) }} ({{ number_format($product->profit_margin, 2) }}%)
                                            </dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Identification -->
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Identification</h3>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Barcode</dt>
                                            <dd class="text-sm text-gray-900 font-mono">{{ $product->barcode }}</dd>
                                        </div>
                                        @if($product->huid_number)
                                            <div>
                                                <dt class="text-sm font-medium text-gray-500">HUID Number</dt>
                                                <dd class="text-sm text-gray-900 font-mono">{{ $product->huid_number }}</dd>
                                            </div>
                                        @endif
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">HSN Code</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->hsn_code }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Added On</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->created_at->format('d M, Y h:i A') }}</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                            <dd class="text-sm text-gray-900">{{ $product->updated_at->format('d M, Y h:i A') }}</dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            @if($product->description)
                                <div class="mt-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">Description</h3>
                                    <p class="text-sm text-gray-700">{{ $product->description }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales History -->
            @if($product->saleItems->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Sales History</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($product->saleItems->take(10) as $saleItem)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ $saleItem->sale->invoice_number }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $saleItem->sale->customer->name }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $saleItem->sale->sale_date->format('d M, Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $saleItem->quantity }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ₹{{ number_format($saleItem->item_total, 2) }}
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Quantity Adjustment Modal -->
    <div id="quantityModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Adjust Quantity</h3>
                <form method="POST" action="{{ route('products.adjust-quantity', $product) }}">
                    @csrf

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Quantity:</label>
                        <div class="text-lg font-semibold text-gray-900">{{ $product->quantity }}</div>
                    </div>

                    <div class="mb-4">
                        <label for="adjustment_type" class="block text-sm font-medium text-gray-700 mb-2">Adjustment Type:</label>
                        <select name="adjustment_type" id="adjustment_type" class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                            <option value="">Choose adjustment type...</option>
                            <option value="increase">Increase Quantity</option>
                            <option value="decrease">Decrease Quantity</option>
                            <option value="set">Set Exact Quantity</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">Quantity:</label>
                        <input type="number" name="quantity" id="quantity" min="0"
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" required>
                    </div>

                    <div class="mb-4">
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">Reason (Optional):</label>
                        <textarea name="reason" id="reason" rows="3"
                                  class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                  placeholder="Enter reason for adjustment..."></textarea>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" id="cancelQuantityAdjustment" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                            Adjust Quantity
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const adjustQuantityBtn = document.getElementById('adjustQuantityBtn');
            const quantityModal = document.getElementById('quantityModal');
            const cancelQuantityAdjustment = document.getElementById('cancelQuantityAdjustment');
            const adjustmentTypeSelect = document.getElementById('adjustment_type');
            const quantityInput = document.getElementById('quantity');

            // Show quantity adjustment modal
            if (adjustQuantityBtn) {
                adjustQuantityBtn.addEventListener('click', function() {
                    quantityModal.classList.remove('hidden');
                });
            }

            // Cancel quantity adjustment
            if (cancelQuantityAdjustment) {
                cancelQuantityAdjustment.addEventListener('click', function() {
                    quantityModal.classList.add('hidden');
                    document.querySelector('form').reset();
                });
            }

            // Close modal when clicking outside
            if (quantityModal) {
                quantityModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.classList.add('hidden');
                        document.querySelector('form').reset();
                    }
                });
            }

            // Update quantity input placeholder based on adjustment type
            if (adjustmentTypeSelect) {
                adjustmentTypeSelect.addEventListener('change', function() {
                    const currentQuantity = {{ $product->quantity }};
                    switch (this.value) {
                        case 'increase':
                            quantityInput.placeholder = 'Amount to add';
                            quantityInput.min = '1';
                            break;
                        case 'decrease':
                            quantityInput.placeholder = 'Amount to subtract';
                            quantityInput.min = '1';
                            quantityInput.max = currentQuantity;
                            break;
                        case 'set':
                            quantityInput.placeholder = 'New total quantity';
                            quantityInput.min = '0';
                            quantityInput.removeAttribute('max');
                            break;
                        default:
                            quantityInput.placeholder = '';
                            quantityInput.min = '0';
                            quantityInput.removeAttribute('max');
                    }
                });
            }
        });
    </script>
</x-app-layout>
