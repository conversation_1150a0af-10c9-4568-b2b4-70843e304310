<x-app-layout>
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="px-4 sm:px-6 lg:px-8 py-6 max-w-9xl mx-auto">
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-slate-800">{{ __('Edit Sale') }} #{{ $sale->invoice_number }}</h1>
                    <span class="inline-flex items-center ml-3 px-2.5 py-0.5 rounded-full text-xs font-medium 
                        {{ $sale->status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ ucfirst($sale->status) }}
                    </span>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('sales.index') }}" 
                        class="btn-sm border-slate-200 hover:border-slate-300 text-slate-600">
                        <svg class="w-4 h-4 fill-current text-slate-500 shrink-0" viewBox="0 0 16 16">
                            <path d="M9.4 13.4l1.4-1.4-4-4 4-4-1.4-1.4L4 8z"></path>
                        </svg>
                        <span class="ml-2">{{ __('Back to Sales') }}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="py-8 max-w-9xl mx-auto px-4 sm:px-6 lg:px-8">
        <form action="{{ route('sales.update', $sale) }}" method="POST" class="space-y-8">
            @csrf
            @method('PUT')
            
            <!-- Customer Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-slate-800 mb-4">{{ __('Customer Information') }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label for="customer_id" class="block text-sm font-medium text-slate-700">{{ __('Customer') }}</label>
                        <select id="customer_id" name="customer_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-slate-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}" {{ $sale->customer_id == $customer->id ? 'selected' : '' }}>
                                    {{ $customer->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="invoice_date" class="block text-sm font-medium text-slate-700">{{ __('Invoice Date') }}</label>
                        <input type="date" name="invoice_date" id="invoice_date" 
                            value="{{ optional($sale->invoice_date)->format('Y-m-d') }}"
                            class="mt-1 block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-slate-700">{{ __('Payment Method') }}</label>
                        <select id="payment_method" name="payment_method" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-slate-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                            <option value="cash" {{ $sale->payment_method === 'cash' ? 'selected' : '' }}>{{ __('Cash') }}</option>
                            <option value="card" {{ $sale->payment_method === 'card' ? 'selected' : '' }}>{{ __('Card') }}</option>
                            <option value="upi" {{ $sale->payment_method === 'upi' ? 'selected' : '' }}>{{ __('UPI') }}</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Sale Items -->
            <div class="bg-white shadow rounded-lg overflow-hidden">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-lg font-medium text-slate-800">{{ __('Sale Items') }}</h2>
                        <button type="button" onclick="addItem()" 
                            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            {{ __('Add Item') }}
                        </button>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-slate-200">
                            <thead class="bg-slate-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                        {{ __('Product') }}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                        {{ __('HUID') }}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                        {{ __('Weight (g)') }}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                        {{ __('Making Charges') }}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                        {{ __('Price') }}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                        {{ __('Actions') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="items-container" class="bg-white divide-y divide-slate-200">
                                @foreach($sale->saleItems as $index => $item)
                                <tr class="sale-item">
                                    <td class="px-6 py-4">
                                        <select name="items[{{ $index }}][product_id]" class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                            @foreach($products as $product)
                                                <option value="{{ $product->id }}" {{ $item->product_id == $product->id ? 'selected' : '' }}>
                                                    {{ $product->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="text" name="items[{{ $index }}][huid]" value="{{ $item->huid }}" 
                                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            required>
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="number" name="items[{{ $index }}][weight]" value="{{ $item->weight }}" 
                                            step="0.001" 
                                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            required 
                                            onchange="calculateItemTotal(this)">
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="number" name="items[{{ $index }}][making_charges]" value="{{ $item->making_charges }}" 
                                            step="0.01"
                                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            required 
                                            onchange="calculateItemTotal(this)">
                                    </td>
                                    <td class="px-6 py-4">
                                        <input type="number" name="items[{{ $index }}][price]" value="{{ $item->price }}" 
                                            step="0.01"
                                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                            readonly>
                                    </td>
                                    <td class="px-6 py-4">
                                        <button type="button" onclick="removeItem(this)" 
                                            class="text-red-600 hover:text-red-900">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-lg font-medium text-slate-800 mb-4">{{ __('Summary') }}</h2>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-slate-600">{{ __('Subtotal') }}</span>
                        <span class="text-sm font-medium text-slate-900">₹<span id="subtotal">{{ number_format($sale->subtotal, 2) }}</span></span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-slate-600">{{ __('GST (3%)') }}</span>
                        <span class="text-sm font-medium text-slate-900">₹<span id="tax">{{ number_format($sale->tax_amount, 2) }}</span></span>
                    </div>
                    <div class="border-t border-slate-200 pt-4 flex justify-between items-center">
                        <span class="text-base font-medium text-slate-900">{{ __('Total') }}</span>
                        <span class="text-base font-medium text-slate-900">₹<span id="total">{{ number_format($sale->total_amount, 2) }}</span></span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('sales.index') }}" 
                    class="inline-flex justify-center py-2 px-4 border border-slate-300 shadow-sm text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ __('Cancel') }}
                </a>
                <button type="submit" 
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    {{ __('Update Sale') }}
                </button>
            </div>
        </form>
    </div>

    @push('scripts')
    <script>
        function calculateItemTotal(input) {
            const row = input.closest('.sale-item');
            const weight = parseFloat(row.querySelector('[name$="[weight]"]').value) || 0;
            const makingCharges = parseFloat(row.querySelector('[name$="[making_charges]"]').value) || 0;
            const priceInput = row.querySelector('[name$="[price]"]');
            
            // Get current gold rate from your application's state
            const goldRate = {{ $currentGoldRate ?? 0 }}; // This should be provided by the controller
            
            const metalValue = weight * goldRate;
            const total = metalValue + makingCharges;
            
            priceInput.value = total.toFixed(2);
            updateTotals();
        }

        function updateTotals() {
            const items = document.querySelectorAll('.sale-item');
            let subtotal = 0;
            
            items.forEach(item => {
                const price = parseFloat(item.querySelector('[name$="[price]"]').value) || 0;
                subtotal += price;
            });
            
            const tax = subtotal * 0.03; // 3% GST
            const total = subtotal + tax;
            
            document.getElementById('subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('tax').textContent = tax.toFixed(2);
            document.getElementById('total').textContent = total.toFixed(2);
        }

        function addItem() {
            const container = document.getElementById('items-container');
            const index = container.children.length;
            const template = `
                <tr class="sale-item">
                    <td class="px-6 py-4">
                        <select name="items[${index}][product_id]" class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            @foreach($products as $product)
                                <option value="{{ $product->id }}">{{ $product->name }}</option>
                            @endforeach
                        </select>
                    </td>
                    <td class="px-6 py-4">
                        <input type="text" name="items[${index}][huid]" 
                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            required>
                    </td>
                    <td class="px-6 py-4">
                        <input type="number" name="items[${index}][weight]" 
                            step="0.001"
                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            required 
                            onchange="calculateItemTotal(this)">
                    </td>
                    <td class="px-6 py-4">
                        <input type="number" name="items[${index}][making_charges]" 
                            step="0.01"
                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            required 
                            onchange="calculateItemTotal(this)">
                    </td>
                    <td class="px-6 py-4">
                        <input type="number" name="items[${index}][price]" 
                            step="0.01"
                            class="block w-full border-slate-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            readonly>
                    </td>
                    <td class="px-6 py-4">
                        <button type="button" onclick="removeItem(this)" 
                            class="text-red-600 hover:text-red-900">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </td>
                </tr>
            `;
            
            container.insertAdjacentHTML('beforeend', template);
        }

        function removeItem(button) {
            const row = button.closest('.sale-item');
            row.remove();
            updateTotals();
        }

        // Initialize Select2 for better dropdown experience
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
                $('#customer_id').select2({
                    theme: 'classic',
                    placeholder: '{{ __("Select a customer") }}',
                    allowClear: true
                });
            }
        });
    </script>
    @endpush
</x-app-layout>
