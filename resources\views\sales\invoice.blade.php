<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ $sale->invoice_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #d4af37;
            margin-bottom: 5px;
        }
        .company-details {
            font-size: 10px;
            color: #666;
        }
        .invoice-details {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .invoice-left, .invoice-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .invoice-right {
            text-align: right;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
        }
        .customer-details, .invoice-info {
            background: #f9f9f9;
            padding: 15px;
            border: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .items-table th {
            background-color: #f2f2f2;
            font-weight: bold;
            font-size: 11px;
        }
        .items-table td {
            font-size: 10px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .summary-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
        }
        .summary-table td {
            padding: 5px 10px;
            border-bottom: 1px solid #eee;
        }
        .summary-table .total-row {
            font-weight: bold;
            border-top: 2px solid #333;
            background-color: #f9f9f9;
        }
        .payment-details {
            margin-top: 20px;
            display: table;
            width: 100%;
        }
        .payment-left, .payment-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
        }
        .terms {
            margin-top: 20px;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">JEWEL PRO</div>
        <div class="company-details">
            Premium Jewelry Store<br>
            123 Main Street, City, State - 123456<br>
            Phone: +91 98765 43210 | Email: <EMAIL><br>
            GSTIN: 29ABCDE1234F1Z5
        </div>
    </div>

    <!-- Invoice Details -->
    <div class="invoice-details">
        <div class="invoice-left">
            <div class="customer-details">
                <div class="section-title">Bill To:</div>
                <strong>{{ $sale->customer->name }}</strong><br>
                Mobile: {{ $sale->customer->mobile }}<br>
                @if($sale->customer->email)
                    Email: {{ $sale->customer->email }}<br>
                @endif
                @if($sale->customer->address)
                    {{ $sale->customer->full_address }}
                @endif
            </div>
        </div>
        <div class="invoice-right">
            <div class="invoice-info">
                <div class="section-title">Invoice Details:</div>
                <strong>Invoice #: {{ $sale->invoice_number }}</strong><br>
                Date: {{ $sale->sale_date->format('d M, Y') }}<br>
                Created By: {{ $sale->createdBy->name }}<br>
                Status: {{ ucfirst($sale->payment_status) }}
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 25%;">Product Details</th>
                <th style="width: 8%;">Qty</th>
                <th style="width: 12%;">Metal Rate</th>
                <th style="width: 12%;">Making</th>
                <th style="width: 10%;">Stone</th>
                <th style="width: 10%;">Wastage</th>
                <th style="width: 8%;">HSN</th>
                <th style="width: 10%;">Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach($sale->saleItems as $index => $item)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>
                        <strong>{{ $item->product->name }}</strong><br>
                        <small>{{ $item->product->metal_type }} {{ $item->product->purity }}</small><br>
                        <small>Weight: {{ $item->product->net_weight }}g</small><br>
                        <small>Barcode: {{ $item->product->barcode }}</small>
                        @if($item->huid_number || $item->product->huid_number)
                            <br><small><strong>HUID: {{ $item->huid_number ?: $item->product->huid_number }}</strong></small>
                        @endif
                        @if($item->huid_required && empty($item->huid_number) && empty($item->product->huid_number))
                            <br><small style="color: red;"><strong>⚠️ HUID Required</strong></small>
                        @endif
                    </td>
                    <td class="text-center">{{ $item->quantity }}</td>
                    <td class="text-right">₹{{ number_format($item->metal_rate, 2) }}</td>
                    <td class="text-right">₹{{ number_format($item->making_charges, 2) }}</td>
                    <td class="text-right">₹{{ number_format($item->stone_charges, 2) }}</td>
                    <td class="text-right">₹{{ number_format($item->wastage_amount, 2) }}</td>
                    <td class="text-center">{{ $item->product->hsn_code }}</td>
                    <td class="text-right"><strong>₹{{ number_format($item->item_total, 2) }}</strong></td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Summary -->
    <table class="summary-table">
        <tr>
            <td>Subtotal:</td>
            <td class="text-right">₹{{ number_format($sale->subtotal, 2) }}</td>
        </tr>
        <tr>
            <td>CGST (1.5%):</td>
            <td class="text-right">₹{{ number_format($sale->cgst_amount, 2) }}</td>
        </tr>
        <tr>
            <td>SGST (1.5%):</td>
            <td class="text-right">₹{{ number_format($sale->sgst_amount, 2) }}</td>
        </tr>
        <tr>
            <td>Total Tax:</td>
            <td class="text-right">₹{{ number_format($sale->total_tax, 2) }}</td>
        </tr>
        @if($sale->discount_amount > 0)
            <tr>
                <td>Discount:</td>
                <td class="text-right" style="color: green;">-₹{{ number_format($sale->discount_amount, 2) }}</td>
            </tr>
        @endif
        <tr class="total-row">
            <td><strong>Total Amount:</strong></td>
            <td class="text-right"><strong>₹{{ number_format($sale->total_amount, 2) }}</strong></td>
        </tr>
    </table>

    <!-- Payment Details -->
    <div class="payment-details">
        <div class="payment-left">
            <div class="section-title">Payment Details:</div>
            @if($sale->cash_payment > 0)
                Cash Payment: ₹{{ number_format($sale->cash_payment, 2) }}<br>
            @endif
            @if($sale->card_payment > 0)
                Card Payment: ₹{{ number_format($sale->card_payment, 2) }}<br>
            @endif
            @if($sale->upi_payment > 0)
                UPI Payment: ₹{{ number_format($sale->upi_payment, 2) }}<br>
            @endif
            @if($sale->old_gold_adjustment > 0)
                Old Gold Adjustment: ₹{{ number_format($sale->old_gold_adjustment, 2) }}<br>
            @endif
            <strong>Total Paid: ₹{{ number_format($sale->total_payment, 2) }}</strong><br>
            @if($sale->balance_amount > 0)
                <span style="color: red;"><strong>Balance Due: ₹{{ number_format($sale->balance_amount, 2) }}</strong></span>
            @endif
        </div>
        <div class="payment-right">
            @if($sale->notes)
                <div class="section-title">Notes:</div>
                {{ $sale->notes }}
            @endif
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="terms">
        <div class="section-title">Terms & Conditions:</div>
        1. All jewelry items are sold with proper certification and quality assurance.<br>
        2. Exchange and return policy applies as per store terms.<br>
        3. This invoice is computer generated and does not require signature.<br>
        4. For any queries, please contact us within 7 days of purchase.<br>
        5. Subject to local jurisdiction only.
    </div>

    <!-- Footer -->
    <div class="footer">
        <strong>Thank you for choosing Jewel Pro!</strong><br>
        This is a computer generated invoice. For any queries, please contact <NAME_EMAIL>
    </div>
</body>
</html>
