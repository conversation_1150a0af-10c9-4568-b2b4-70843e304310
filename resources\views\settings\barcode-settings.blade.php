<x-app-layout>
    <x-page-header>
        <x-slot name="title">{{ __('Barcode Settings') }}</x-slot>
        <x-slot name="description">{{ __('Configure barcode format, dimensions, and label printing preferences for your inventory.') }}</x-slot>
        <x-slot name="icon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v-4m6 0h-2m2 0v4m-6 0h-2m2 0v4m-6-4h2m-2 0v4m0-11v-4m0 0h2m-2 0v4m0 0h2m-2 0v4m6-4h2m-2 0v4m0-11v-4m0 0h2m-2 0v4m0 0h2m-2 0v4m6-4h2m-2 0v4" />
            </svg>
        </x-slot>
        <x-slot name="breadcrumbs">
            <li><a href="{{ route('settings.index') }}" class="hover:text-gray-700">{{ __('Settings') }}</a></li>
            <li class="px-2 text-gray-400">/</li>
            <li class="text-gray-600">{{ __('Barcode') }}</li>
        </x-slot>
    </x-page-header>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('settings.update-barcode-settings') }}" class="space-y-6">
                        @csrf

                        <div>
                            <x-input-label for="barcode_format" value="{{ __('Barcode Format') }}" />
                            <select id="barcode_format" name="barcode_format" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="CODE128" {{ old('barcode_format', $settings['barcode_format'] ?? '') === 'CODE128' ? 'selected' : '' }}>{{ __('Code 128') }}</option>
                                <option value="CODE39" {{ old('barcode_format', $settings['barcode_format'] ?? '') === 'CODE39' ? 'selected' : '' }}>{{ __('Code 39') }}</option>
                                <option value="EAN13" {{ old('barcode_format', $settings['barcode_format'] ?? '') === 'EAN13' ? 'selected' : '' }}>{{ __('EAN-13') }}</option>
                                <option value="EAN8" {{ old('barcode_format', $settings['barcode_format'] ?? '') === 'EAN8' ? 'selected' : '' }}>{{ __('EAN-8') }}</option>
                            </select>
                            <x-input-error :messages="$errors->get('barcode_format')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="barcode_prefix" value="{{ __('Barcode Prefix') }}" />
                            <x-text-input id="barcode_prefix" name="barcode_prefix" type="text" maxlength="5"
                                class="mt-1 block w-full"
                                value="{{ old('barcode_prefix', $settings['barcode_prefix'] ?? 'JP') }}" />
                            <p class="mt-1 text-sm text-gray-500">{{ __('Maximum 5 characters') }}</p>
                            <x-input-error :messages="$errors->get('barcode_prefix')" class="mt-2" />
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <x-input-label for="barcode_width" value="{{ __('Barcode Width') }}" />
                                <x-text-input id="barcode_width" name="barcode_width" type="number" min="1" max="10"
                                    class="mt-1 block w-full"
                                    value="{{ old('barcode_width', $settings['barcode_width'] ?? 2) }}" />
                                <p class="mt-1 text-sm text-gray-500">{{ __('Value between 1-10') }}</p>
                                <x-input-error :messages="$errors->get('barcode_width')" class="mt-2" />
                            </div>

                            <div>
                                <x-input-label for="barcode_height" value="{{ __('Barcode Height') }}" />
                                <x-text-input id="barcode_height" name="barcode_height" type="number" min="20" max="200"
                                    class="mt-1 block w-full"
                                    value="{{ old('barcode_height', $settings['barcode_height'] ?? 50) }}" />
                                <p class="mt-1 text-sm text-gray-500">{{ __('Value between 20-200') }}</p>
                                <x-input-error :messages="$errors->get('barcode_height')" class="mt-2" />
                            </div>
                        </div>

                        <div>
                            <x-input-label for="label_template" value="{{ __('Label Template') }}" />
                            <select id="label_template" name="label_template" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="small" {{ old('label_template', $settings['label_template'] ?? '') === 'small' ? 'selected' : '' }}>{{ __('Small Label') }}</option>
                                <option value="medium" {{ old('label_template', $settings['label_template'] ?? '') === 'medium' ? 'selected' : '' }}>{{ __('Medium Label') }}</option>
                                <option value="large" {{ old('label_template', $settings['label_template'] ?? '') === 'large' ? 'selected' : '' }}>{{ __('Large Label') }}</option>
                                <option value="custom" {{ old('label_template', $settings['label_template'] ?? '') === 'custom' ? 'selected' : '' }}>{{ __('Custom Label') }}</option>
                            </select>
                            <x-input-error :messages="$errors->get('label_template')" class="mt-2" />
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="auto_generate_barcode" name="auto_generate_barcode" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('auto_generate_barcode', $settings['auto_generate_barcode'] ?? true) ? 'checked' : '' }}>
                                <label for="auto_generate_barcode" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Auto Generate Barcode for New Products') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="include_price_on_label" name="include_price_on_label" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('include_price_on_label', $settings['include_price_on_label'] ?? true) ? 'checked' : '' }}>
                                <label for="include_price_on_label" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Include Price on Label') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="include_weight_on_label" name="include_weight_on_label" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('include_weight_on_label', $settings['include_weight_on_label'] ?? true) ? 'checked' : '' }}>
                                <label for="include_weight_on_label" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Include Weight on Label') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="include_huid_on_label" name="include_huid_on_label" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('include_huid_on_label', $settings['include_huid_on_label'] ?? true) ? 'checked' : '' }}>
                                <label for="include_huid_on_label" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Include HUID on Label') }}
                                </label>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end">
                            <x-primary-button>
                                {{ __('Save Settings') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
