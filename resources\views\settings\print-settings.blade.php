<x-app-layout>
    <x-page-header>
        <x-slot name="title">{{ __('Print Settings') }}</x-slot>
        <x-slot name="description">{{ __('Customize invoice templates, receipt formats, and other printing preferences.') }}</x-slot>
        <x-slot name="icon">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
            </svg>
        </x-slot>
        <x-slot name="breadcrumbs">
            <li><a href="{{ route('settings.index') }}" class="hover:text-gray-700">{{ __('Settings') }}</a></li>
            <li class="px-2 text-gray-400">/</li>
            <li class="text-gray-600">{{ __('Print') }}</li>
        </x-slot>
    </x-page-header>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('settings.update-print-settings') }}" class="space-y-6">
                        @csrf

                        <div>
                            <x-input-label for="invoice_template" value="{{ __('Invoice Template') }}" />
                            <select id="invoice_template" name="invoice_template" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="template1" {{ old('invoice_template', $settings['invoice_template'] ?? '') === 'template1' ? 'selected' : '' }}>{{ __('Standard Template') }}</option>
                                <option value="template2" {{ old('invoice_template', $settings['invoice_template'] ?? '') === 'template2' ? 'selected' : '' }}>{{ __('Professional Template') }}</option>
                                <option value="template3" {{ old('invoice_template', $settings['invoice_template'] ?? '') === 'template3' ? 'selected' : '' }}>{{ __('Compact Template') }}</option>
                            </select>
                            <x-input-error :messages="$errors->get('invoice_template')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="receipt_paper_size" value="{{ __('Receipt Paper Size') }}" />
                            <select id="receipt_paper_size" name="receipt_paper_size" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="A4" {{ old('receipt_paper_size', $settings['receipt_paper_size'] ?? '') === 'A4' ? 'selected' : '' }}>{{ __('A4') }}</option>
                                <option value="A5" {{ old('receipt_paper_size', $settings['receipt_paper_size'] ?? '') === 'A5' ? 'selected' : '' }}>{{ __('A5') }}</option>
                                <option value="thermal_80mm" {{ old('receipt_paper_size', $settings['receipt_paper_size'] ?? '') === 'thermal_80mm' ? 'selected' : '' }}>{{ __('Thermal 80mm') }}</option>
                                <option value="thermal_58mm" {{ old('receipt_paper_size', $settings['receipt_paper_size'] ?? '') === 'thermal_58mm' ? 'selected' : '' }}>{{ __('Thermal 58mm') }}</option>
                            </select>
                            <x-input-error :messages="$errors->get('receipt_paper_size')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="invoice_copies" value="{{ __('Number of Invoice Copies') }}" />
                            <x-text-input id="invoice_copies" name="invoice_copies" type="number" min="1" max="5"
                                class="mt-1 block w-full"
                                value="{{ old('invoice_copies', $settings['invoice_copies'] ?? 1) }}" />
                            <x-input-error :messages="$errors->get('invoice_copies')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="invoice_footer_text" value="{{ __('Invoice Footer Text') }}" />
                            <textarea id="invoice_footer_text" name="invoice_footer_text" rows="3"
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('invoice_footer_text', $settings['invoice_footer_text'] ?? 'Thank you for your business!') }}</textarea>
                            <x-input-error :messages="$errors->get('invoice_footer_text')" class="mt-2" />
                        </div>

                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="print_logo_on_invoice" name="print_logo_on_invoice" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('print_logo_on_invoice', $settings['print_logo_on_invoice'] ?? false) ? 'checked' : '' }}>
                                <label for="print_logo_on_invoice" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Print Logo on Invoice') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="print_terms_conditions" name="print_terms_conditions" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('print_terms_conditions', $settings['print_terms_conditions'] ?? false) ? 'checked' : '' }}>
                                <label for="print_terms_conditions" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Print Terms & Conditions') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="auto_print_invoice" name="auto_print_invoice" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('auto_print_invoice', $settings['auto_print_invoice'] ?? false) ? 'checked' : '' }}>
                                <label for="auto_print_invoice" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Auto-Print Invoice after Sale') }}
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="print_barcode_on_invoice" name="print_barcode_on_invoice" value="1"
                                    class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"
                                    {{ old('print_barcode_on_invoice', $settings['print_barcode_on_invoice'] ?? false) ? 'checked' : '' }}>
                                <label for="print_barcode_on_invoice" class="ml-2 block text-sm text-gray-900">
                                    {{ __('Print Barcode on Invoice') }}
                                </label>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-end">
                            <x-primary-button>
                                {{ __('Save Settings') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
