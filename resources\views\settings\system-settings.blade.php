<x-app-layout>
    <x-page-header>
        <x-slot name="title">{{ __('System Settings') }}</x-slot>
        <x-slot name="description">{{ __('Configure system-wide settings including currency, date formats, language, and more.') }}</x-slot>
        <x-slot name="icon">
            <x-icons.settings class="h-8 w-8" />
        </x-slot>
        <x-slot name="breadcrumbs">
            <li><a href="{{ route('settings.index') }}" class="hover:text-gray-700">{{ __('Settings') }}</a></li>
            <li class="px-2 text-gray-400">/</li>
            <li class="text-gray-600">{{ __('System') }}</li>
        </x-slot>
    </x-page-header>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('settings.update-system-settings') }}" class="space-y-6">
                        @csrf

                        <!-- Currency Settings -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <x-input-label for="currency_symbol" value="{{ __('Currency Symbol') }}" />
                                <x-text-input id="currency_symbol" name="currency_symbol" type="text" maxlength="5"
                                    class="mt-1 block w-full"
                                    value="{{ old('currency_symbol', $settings['currency_symbol'] ?? '₹') }}" />
                                <x-input-error :messages="$errors->get('currency_symbol')" class="mt-2" />
                            </div>

                            <div>
                                <x-input-label for="currency_position" value="{{ __('Currency Position') }}" />
                                <select id="currency_position" name="currency_position" 
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="before" {{ old('currency_position', $settings['currency_position'] ?? '') === 'before' ? 'selected' : '' }}>
                                        {{ __('Before Amount (₹100)') }}
                                    </option>
                                    <option value="after" {{ old('currency_position', $settings['currency_position'] ?? '') === 'after' ? 'selected' : '' }}>
                                        {{ __('After Amount (100₹)') }}
                                    </option>
                                </select>
                                <x-input-error :messages="$errors->get('currency_position')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Number Format -->
                        <div>
                            <x-input-label for="decimal_places" value="{{ __('Decimal Places') }}" />
                            <select id="decimal_places" name="decimal_places" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @foreach(range(0, 4) as $decimal)
                                    <option value="{{ $decimal }}" {{ old('decimal_places', $settings['decimal_places'] ?? 2) == $decimal ? 'selected' : '' }}>
                                        {{ $decimal }}
                                    </option>
                                @endforeach
                            </select>
                            <x-input-error :messages="$errors->get('decimal_places')" class="mt-2" />
                        </div>

                        <!-- Date & Time Settings -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <x-input-label for="date_format" value="{{ __('Date Format') }}" />
                                <select id="date_format" name="date_format" 
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="d/m/Y" {{ old('date_format', $settings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : '' }}>
                                        {{ __('DD/MM/YYYY (31/12/2025)') }}
                                    </option>
                                    <option value="m/d/Y" {{ old('date_format', $settings['date_format'] ?? '') === 'm/d/Y' ? 'selected' : '' }}>
                                        {{ __('MM/DD/YYYY (12/31/2025)') }}
                                    </option>
                                    <option value="Y-m-d" {{ old('date_format', $settings['date_format'] ?? '') === 'Y-m-d' ? 'selected' : '' }}>
                                        {{ __('YYYY-MM-DD (2025-12-31)') }}
                                    </option>
                                    <option value="d-m-Y" {{ old('date_format', $settings['date_format'] ?? '') === 'd-m-Y' ? 'selected' : '' }}>
                                        {{ __('DD-MM-YYYY (31-12-2025)') }}
                                    </option>
                                </select>
                                <x-input-error :messages="$errors->get('date_format')" class="mt-2" />
                            </div>

                            <div>
                                <x-input-label for="time_format" value="{{ __('Time Format') }}" />
                                <select id="time_format" name="time_format" 
                                    class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="12" {{ old('time_format', $settings['time_format'] ?? '') === '12' ? 'selected' : '' }}>
                                        {{ __('12 Hour (02:30 PM)') }}
                                    </option>
                                    <option value="24" {{ old('time_format', $settings['time_format'] ?? '') === '24' ? 'selected' : '' }}>
                                        {{ __('24 Hour (14:30)') }}
                                    </option>
                                </select>
                                <x-input-error :messages="$errors->get('time_format')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Timezone -->
                        <div>
                            <x-input-label for="timezone" value="{{ __('Timezone') }}" />
                            <select id="timezone" name="timezone" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="Asia/Kolkata" {{ old('timezone', $settings['timezone'] ?? '') === 'Asia/Kolkata' ? 'selected' : '' }}>
                                    {{ __('India (Asia/Kolkata)') }}
                                </option>
                                <!-- Add more timezone options if needed -->
                            </select>
                            <x-input-error :messages="$errors->get('timezone')" class="mt-2" />
                        </div>

                        <!-- Language -->
                        <div>
                            <x-input-label for="language" value="{{ __('Language') }}" />
                            <select id="language" name="language" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="en" {{ old('language', $settings['language'] ?? '') === 'en' ? 'selected' : '' }}>
                                    {{ __('English') }}
                                </option>
                                <option value="hi" {{ old('language', $settings['language'] ?? '') === 'hi' ? 'selected' : '' }}>
                                    {{ __('Hindi') }}
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('language')" class="mt-2" />
                        </div>

                        <!-- Backup Settings -->
                        <div>
                            <x-input-label for="backup_frequency" value="{{ __('Backup Frequency') }}" />
                            <select id="backup_frequency" name="backup_frequency" 
                                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="daily" {{ old('backup_frequency', $settings['backup_frequency'] ?? '') === 'daily' ? 'selected' : '' }}>
                                    {{ __('Daily') }}
                                </option>
                                <option value="weekly" {{ old('backup_frequency', $settings['backup_frequency'] ?? '') === 'weekly' ? 'selected' : '' }}>
                                    {{ __('Weekly') }}
                                </option>
                                <option value="monthly" {{ old('backup_frequency', $settings['backup_frequency'] ?? '') === 'monthly' ? 'selected' : '' }}>
                                    {{ __('Monthly') }}
                                </option>
                            </select>
                            <x-input-error :messages="$errors->get('backup_frequency')" class="mt-2" />
                        </div>

                        <!-- Inventory Alert -->
                        <div>
                            <x-input-label for="low_stock_threshold" value="{{ __('Low Stock Alert Threshold') }}" />
                            <x-text-input id="low_stock_threshold" name="low_stock_threshold" type="number" min="1" max="100"
                                class="mt-1 block w-full"
                                value="{{ old('low_stock_threshold', $settings['low_stock_threshold'] ?? 5) }}" />
                            <p class="mt-1 text-sm text-gray-500">{{ __('Get alerts when product quantity falls below this number') }}</p>
                            <x-input-error :messages="$errors->get('low_stock_threshold')" class="mt-2" />
                        </div>

                        <div class="mt-6 flex justify-end">
                            <x-primary-button>
                                {{ __('Save Settings') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
