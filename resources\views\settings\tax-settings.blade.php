<x-app-layout>
    <x-page-header>
        <x-slot name="title">{{ __('Tax Settings') }}</x-slot>
        <x-slot name="description">{{ __('Configure GST rates, HSN codes, and other tax-related settings for your jewelry business.') }}</x-slot>
        <x-slot name="icon">
            <x-icons.settings class="h-8 w-8" />
        </x-slot>
        <x-slot name="breadcrumbs">
            <li><a href="{{ route('settings.index') }}" class="hover:text-gray-700">{{ __('Settings') }}</a></li>
            <li class="px-2 text-gray-400">/</li>
            <li class="text-gray-600">{{ __('Tax') }}</li>
        </x-slot>
    </x-page-header>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('settings.update-tax-settings') }}" class="space-y-6">
                        @csrf

                        <div>
                            <x-input-label for="cgst_rate" value="{{ __('CGST Rate (%)') }}" />
                            <x-text-input id="cgst_rate" name="cgst_rate" type="number" step="0.01" 
                                class="mt-1 block w-full"
                                value="{{ old('cgst_rate', $settings['cgst_rate'] ?? 1.5) }}" />
                            <x-input-error :messages="$errors->get('cgst_rate')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="sgst_rate" value="{{ __('SGST Rate (%)') }}" />
                            <x-text-input id="sgst_rate" name="sgst_rate" type="number" step="0.01" 
                                class="mt-1 block w-full"
                                value="{{ old('sgst_rate', $settings['sgst_rate'] ?? 1.5) }}" />
                            <x-input-error :messages="$errors->get('sgst_rate')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="igst_rate" value="{{ __('IGST Rate (%)') }}" />
                            <x-text-input id="igst_rate" name="igst_rate" type="number" step="0.01" 
                                class="mt-1 block w-full"
                                value="{{ old('igst_rate', $settings['igst_rate'] ?? 3.0) }}" />
                            <x-input-error :messages="$errors->get('igst_rate')" class="mt-2" />
                        </div>

                        <div class="mt-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" 
                                    name="tax_inclusive_pricing"
                                    value="1"
                                    {{ old('tax_inclusive_pricing', $settings['tax_inclusive_pricing'] ?? false) ? 'checked' : '' }}>
                                <span class="ml-2 text-sm text-gray-600">{{ __('Tax Inclusive Pricing') }}</span>
                            </label>
                        </div>

                        <div class="mt-4">
                            <x-input-label for="hsn_code_gold" value="{{ __('HSN Code (Gold)') }}" />
                            <x-text-input id="hsn_code_gold" name="hsn_code_gold" type="text" 
                                class="mt-1 block w-full"
                                value="{{ old('hsn_code_gold', $settings['hsn_code_gold'] ?? '71131900') }}" />
                            <x-input-error :messages="$errors->get('hsn_code_gold')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="hsn_code_silver" value="{{ __('HSN Code (Silver)') }}" />
                            <x-text-input id="hsn_code_silver" name="hsn_code_silver" type="text" 
                                class="mt-1 block w-full"
                                value="{{ old('hsn_code_silver', $settings['hsn_code_silver'] ?? '71141100') }}" />
                            <x-input-error :messages="$errors->get('hsn_code_silver')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="hsn_code_diamond" value="{{ __('HSN Code (Diamond)') }}" />
                            <x-text-input id="hsn_code_diamond" name="hsn_code_diamond" type="text" 
                                class="mt-1 block w-full"
                                value="{{ old('hsn_code_diamond', $settings['hsn_code_diamond'] ?? '71023100') }}" />
                            <x-input-error :messages="$errors->get('hsn_code_diamond')" class="mt-2" />
                        </div>

                        <div>
                            <x-input-label for="hsn_code_making_charges" value="{{ __('HSN Code (Making Charges)') }}" />
                            <x-text-input id="hsn_code_making_charges" name="hsn_code_making_charges" type="text" 
                                class="mt-1 block w-full"
                                value="{{ old('hsn_code_making_charges', $settings['hsn_code_making_charges'] ?? '99954') }}" />
                            <x-input-error :messages="$errors->get('hsn_code_making_charges')" class="mt-2" />
                        </div>

                        <div class="mt-6 flex justify-end">
                            <x-primary-button>
                                {{ __('Save Settings') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
