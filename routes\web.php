<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\OldGoldPurchaseController;
use App\Http\Controllers\EstimateController;
use App\Http\Controllers\RepairController;
use App\Http\Controllers\BarcodeController;
use App\Http\Controllers\MetalRateController;
use App\Http\Controllers\SavingSchemeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SettingsController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Customer Management
    Route::resource('customers', CustomerController::class);

    // Inventory Management
    Route::resource('categories', CategoryController::class);
    Route::post('categories/{category}/toggle-active', [CategoryController::class, 'toggleActive'])->name('categories.toggle-active');
    Route::post('categories/bulk-action', [CategoryController::class, 'bulkAction'])->name('categories.bulk-action');

    Route::resource('products', ProductController::class);
    Route::get('products/{product}/barcode', [ProductController::class, 'barcode'])->name('products.barcode');
    Route::post('products/{product}/toggle-status', [ProductController::class, 'toggleStatus'])->name('products.toggle-status');
    Route::post('products/{product}/adjust-quantity', [ProductController::class, 'adjustQuantity'])->name('products.adjust-quantity');
    Route::post('products/bulk-action', [ProductController::class, 'bulkAction'])->name('products.bulk-action');
    Route::get('inventory/report', [ProductController::class, 'inventoryReport'])->name('inventory.report');

    // Sales & Billing
    Route::resource('sales', SaleController::class);
    Route::get('sales/{sale}/invoice', [SaleController::class, 'invoice'])->name('sales.invoice');
    Route::get('sales/{sale}/print', [SaleController::class, 'print'])->name('sales.print');

    // Old Gold Purchase & Exchange
    Route::resource('old-gold-purchases', OldGoldPurchaseController::class);
    Route::get('old-gold-purchases/customer/{customer}', [OldGoldPurchaseController::class, 'getAvailableForCustomer'])->name('old-gold-purchases.customer');

    // Estimates
    Route::resource('estimates', EstimateController::class);
    Route::get('estimates/{estimate}/convert', [EstimateController::class, 'convert'])->name('estimates.convert');
    Route::post('estimates/{estimate}/convert', [EstimateController::class, 'processConversion'])->name('estimates.process-conversion');
    Route::get('estimates/{estimate}/print', [EstimateController::class, 'print'])->name('estimates.print');

    // Repairs
    Route::resource('repairs', RepairController::class);
    Route::patch('repairs/{repair}/status', [RepairController::class, 'updateStatus'])->name('repairs.update-status');
    Route::get('repairs/{repair}/receipt', [RepairController::class, 'receipt'])->name('repairs.receipt');
    Route::get('repairs/{repair}/delivery-slip', [RepairController::class, 'deliverySlip'])->name('repairs.delivery-slip');

    // Barcodes
    Route::get('barcodes', [BarcodeController::class, 'index'])->name('barcodes.index');
    Route::get('barcodes/scanner', [BarcodeController::class, 'scanner'])->name('barcodes.scanner');
    Route::get('barcodes/{product}/generate', [BarcodeController::class, 'generate'])->name('barcodes.generate');
    Route::post('barcodes/generate-bulk', [BarcodeController::class, 'generateBulk'])->name('barcodes.generate-bulk');
    Route::post('barcodes/print-labels', [BarcodeController::class, 'printLabels'])->name('barcodes.print-labels');
    Route::post('barcodes/scan', [BarcodeController::class, 'scan'])->name('barcodes.scan');
    Route::post('barcodes/{product}/generate-new', [BarcodeController::class, 'generateNewBarcode'])->name('barcodes.generate-new');
    Route::post('barcodes/generate-missing', [BarcodeController::class, 'generateMissingBarcodes'])->name('barcodes.generate-missing');

    // Metal Rates & Calculator
    Route::resource('metal-rates', MetalRateController::class);
    Route::post('metal-rates/{metalRate}/toggle-active', [MetalRateController::class, 'toggleActive'])->name('metal-rates.toggle-active');
    Route::post('metal-rates/bulk-action', [MetalRateController::class, 'bulkAction'])->name('metal-rates.bulk-action');
    Route::get('calculator', [MetalRateController::class, 'calculator'])->name('calculator');
    Route::post('calculator/calculate', [MetalRateController::class, 'calculatePrice'])->name('calculator.calculate');
    Route::get('api/metal-rates/current', [MetalRateController::class, 'getCurrentRates'])->name('api.metal-rates.current');
    Route::post('metal-rates/bulk-update', [MetalRateController::class, 'bulkUpdate'])->name('metal-rates.bulk-update');

    // Saving Schemes
    Route::resource('saving-schemes', SavingSchemeController::class);
    Route::get('saving-schemes/{savingScheme}/add-payment', [SavingSchemeController::class, 'addPayment'])->name('saving-schemes.add-payment');
    Route::post('saving-schemes/{savingScheme}/add-payment', [SavingSchemeController::class, 'storePayment'])->name('saving-schemes.store-payment');
    Route::get('saving-schemes/{savingScheme}/certificate', [SavingSchemeController::class, 'certificate'])->name('saving-schemes.certificate');
    Route::get('api/saving-schemes/summary', [SavingSchemeController::class, 'getSchemeSummary'])->name('api.saving-schemes.summary');

    // Reports & Analytics
    Route::get('reports', [ReportController::class, 'index'])->name('reports.index');
    Route::get('reports/sales', [ReportController::class, 'salesReport'])->name('reports.sales');
    Route::get('reports/inventory', [ReportController::class, 'inventoryReport'])->name('reports.inventory');
    Route::get('reports/customers', [ReportController::class, 'customerReport'])->name('reports.customers');
    Route::get('reports/repairs', [ReportController::class, 'repairReport'])->name('reports.repairs');
    Route::get('reports/schemes', [ReportController::class, 'schemeReport'])->name('reports.schemes');
    Route::get('reports/financial', [ReportController::class, 'financialReport'])->name('reports.financial');
    Route::post('reports/export-pdf', [ReportController::class, 'exportPdf'])->name('reports.export-pdf');

    // Settings & Configuration
    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::get('settings/business-profile', [SettingsController::class, 'businessProfile'])->name('settings.business-profile');
    Route::post('settings/business-profile', [SettingsController::class, 'updateBusinessProfile'])->name('settings.update-business-profile');
    Route::get('settings/tax-settings', [SettingsController::class, 'taxSettings'])->name('settings.tax-settings');
    Route::post('settings/tax-settings', [SettingsController::class, 'updateTaxSettings'])->name('settings.update-tax-settings');
    Route::get('settings/print-settings', [SettingsController::class, 'printSettings'])->name('settings.print-settings');
    Route::post('settings/print-settings', [SettingsController::class, 'updatePrintSettings'])->name('settings.update-print-settings');
    Route::get('settings/barcode-settings', [SettingsController::class, 'barcodeSettings'])->name('settings.barcode-settings');
    Route::post('settings/barcode-settings', [SettingsController::class, 'updateBarcodeSettings'])->name('settings.update-barcode-settings');
    Route::get('settings/system-settings', [SettingsController::class, 'systemSettings'])->name('settings.system-settings');
    Route::post('settings/system-settings', [SettingsController::class, 'updateSystemSettings'])->name('settings.update-system-settings');

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
