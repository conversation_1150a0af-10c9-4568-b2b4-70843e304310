<?php

namespace Tests\Unit;

use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SaleTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_calculates_total_amount_correctly()
    {
        $product1 = Product::factory()->create(['price' => 100.00]);
        $product2 = Product::factory()->create(['price' => 200.00]);
        
        $sale = Sale::factory()->create();
        
        $sale->items()->create([
            'product_id' => $product1->id,
            'quantity' => 2,
            'price' => $product1->price
        ]);
        
        $sale->items()->create([
            'product_id' => $product2->id,
            'quantity' => 1,
            'price' => $product2->price
        ]);

        $this->assertEquals(400.00, $sale->total_amount);
    }

    /** @test */
    public function it_validates_product_availability()
    {
        $product = Product::factory()->create(['stock' => 5]);
        $sale = Sale::factory()->create();

        $result = $sale->canAddProduct($product, 6);
        
        $this->assertFalse($result);
        $this->assertTrue($sale->canAddProduct($product, 5));
    }

    /** @test */
    public function it_updates_product_stock_after_sale()
    {
        $product = Product::factory()->create(['stock' => 10]);
        $sale = Sale::factory()->create();
        
        $sale->items()->create([
            'product_id' => $product->id,
            'quantity' => 3,
            'price' => $product->price
        ]);

        $product->refresh();
        $this->assertEquals(7, $product->stock);
    }

    /** @test */
    public function it_calculates_customer_total_purchases()
    {
        $customer = Customer::factory()->create();
        $sales = Sale::factory()->count(3)->create([
            'customer_id' => $customer->id,
            'total_amount' => 100.00
        ]);

        $this->assertEquals(300.00, $customer->totalPurchases());
    }
}
